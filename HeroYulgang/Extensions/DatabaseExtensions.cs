using System;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang.Extensions
{
    /// <summary>
    /// Extension methods để game server có thể sử dụng database metrics một cách an toàn
    /// </summary>
    public static class DatabaseExtensions
    {
        /// <summary>
        /// Kiểm tra database health cho game server
        /// </summary>
        public static bool IsDatabaseHealthy()
        {
            try
            {
                return DatabaseMetricsService.Instance.IsDatabaseHealthy();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Lấy database metrics summary cho logging
        /// </summary>
        public static string GetDatabaseSummary()
        {
            try
            {
                var metrics = DatabaseMetricsService.Instance.GetMetricsForGameServer();
                return metrics.ToSummaryString();
            }
            catch (Exception ex)
            {
                return $"DB Metrics Error: {ex.Message}";
            }
        }

        /// <summary>
        /// Kiểm tra xem có nên thực hiện database operation không
        /// </summary>
        public static bool ShouldExecuteDatabaseOperation()
        {
            try
            {
                var metrics = DatabaseMetricsService.Instance.GetMetricsForGameServer();
                
                // Không thực hiện nếu:
                // - Quá nhiều pending queries (>100)
                // - Connection utilization quá cao (>95%)
                // - Database không healthy
                return metrics.IsHealthy && 
                       metrics.PendingQueries < 100 && 
                       metrics.ConnectionUtilizationRate < 95;
            }
            catch
            {
                return false; // Safe default
            }
        }

        /// <summary>
        /// Lấy delay time cho database operations dựa trên load
        /// </summary>
        public static int GetRecommendedDelayMs()
        {
            try
            {
                var metrics = DatabaseMetricsService.Instance.GetMetricsForGameServer();
                
                // Tăng delay dựa trên pending queries và utilization
                var baseDelay = 0;
                
                if (metrics.PendingQueries > 50)
                    baseDelay += 100;
                
                if (metrics.PendingQueries > 100)
                    baseDelay += 200;
                
                if (metrics.ConnectionUtilizationRate > 80)
                    baseDelay += 150;
                
                if (metrics.ConnectionUtilizationRate > 90)
                    baseDelay += 300;
                
                if (metrics.AverageExecutionTimeMs > 1000)
                    baseDelay += 200;
                
                return Math.Min(baseDelay, 1000); // Cap at 1 second
            }
            catch
            {
                return 100; // Safe default delay
            }
        }

        /// <summary>
        /// Async wrapper cho database operations với automatic retry
        /// </summary>
        public static async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
        {
            var retryCount = 0;
            
            while (retryCount < maxRetries)
            {
                try
                {
                    // Kiểm tra xem có nên thực hiện operation không
                    if (!ShouldExecuteDatabaseOperation())
                    {
                        var delay = GetRecommendedDelayMs();
                        await Task.Delay(delay);
                    }
                    
                    return await operation();
                }
                catch (Exception ex)
                {
                    retryCount++;
                    
                    if (retryCount >= maxRetries)
                    {
                        Logger.Instance.Error($"Database operation failed after {maxRetries} retries: {ex.Message}");
                        throw;
                    }
                    
                    // Exponential backoff
                    var backoffDelay = (int)Math.Pow(2, retryCount) * 1000;
                    Logger.Instance.Warning($"Database operation failed, retrying in {backoffDelay}ms (attempt {retryCount}/{maxRetries})");
                    await Task.Delay(backoffDelay);
                }
            }
            
            throw new InvalidOperationException("Should not reach here");
        }

        /// <summary>
        /// Sync wrapper cho database operations với automatic retry
        /// </summary>
        public static T ExecuteWithRetry<T>(Func<T> operation, int maxRetries = 3)
        {
            var retryCount = 0;
            
            while (retryCount < maxRetries)
            {
                try
                {
                    // Kiểm tra xem có nên thực hiện operation không
                    if (!ShouldExecuteDatabaseOperation())
                    {
                        var delay = GetRecommendedDelayMs();
                        System.Threading.Thread.Sleep(delay);
                    }
                    
                    return operation();
                }
                catch (Exception ex)
                {
                    retryCount++;
                    
                    if (retryCount >= maxRetries)
                    {
                        Logger.Instance.Error($"Database operation failed after {maxRetries} retries: {ex.Message}");
                        throw;
                    }
                    
                    // Exponential backoff
                    var backoffDelay = (int)Math.Pow(2, retryCount) * 1000;
                    Logger.Instance.Warning($"Database operation failed, retrying in {backoffDelay}ms (attempt {retryCount}/{maxRetries})");
                    System.Threading.Thread.Sleep(backoffDelay);
                }
            }
            
            throw new InvalidOperationException("Should not reach here");
        }

        /// <summary>
        /// Log database metrics periodically
        /// </summary>
        public static void LogDatabaseMetrics()
        {
            try
            {
                var summary = GetDatabaseSummary();
                Logger.Instance.Info($"Database Metrics: {summary}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Failed to log database metrics: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra xem có cần alert về database performance không
        /// </summary>
        public static bool ShouldAlertDatabasePerformance()
        {
            try
            {
                var metrics = DatabaseMetricsService.Instance.GetMetricsForGameServer();
                
                return !metrics.IsHealthy ||
                       metrics.PendingQueries > 150 ||
                       metrics.ConnectionUtilizationRate > 95 ||
                       metrics.FailedQueries > 50 ||
                       metrics.AverageExecutionTimeMs > 2000;
            }
            catch
            {
                return true; // Alert on error
            }
        }

        /// <summary>
        /// Lấy performance grade (A-F) cho database
        /// </summary>
        public static string GetDatabasePerformanceGrade()
        {
            try
            {
                var metrics = DatabaseMetricsService.Instance.GetMetricsForGameServer();
                
                if (!metrics.IsHealthy)
                    return "F";
                
                var score = 100;
                
                // Deduct points based on metrics
                if (metrics.PendingQueries > 20) score -= 10;
                if (metrics.PendingQueries > 50) score -= 20;
                if (metrics.PendingQueries > 100) score -= 30;
                
                if (metrics.ConnectionUtilizationRate > 70) score -= 10;
                if (metrics.ConnectionUtilizationRate > 85) score -= 20;
                if (metrics.ConnectionUtilizationRate > 95) score -= 30;
                
                if (metrics.AverageExecutionTimeMs > 500) score -= 10;
                if (metrics.AverageExecutionTimeMs > 1000) score -= 20;
                if (metrics.AverageExecutionTimeMs > 2000) score -= 30;
                
                if (metrics.FailedQueries > 10) score -= 15;
                if (metrics.FailedQueries > 25) score -= 25;
                
                return score switch
                {
                    >= 90 => "A",
                    >= 80 => "B",
                    >= 70 => "C",
                    >= 60 => "D",
                    _ => "F"
                };
            }
            catch
            {
                return "F";
            }
        }
    }
}
