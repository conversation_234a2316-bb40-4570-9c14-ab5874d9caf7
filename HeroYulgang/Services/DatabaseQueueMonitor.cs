using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Threading;

namespace HeroYulgang.Services
{
    public class DatabaseQueueStats
    {
        public int PendingQueries { get; set; }
        public int ExecutingQueries { get; set; }
        public int CompletedQueries { get; set; }
        public int FailedQueries { get; set; }
        public double AverageExecutionTimeMs { get; set; }
        public DateTime LastUpdate { get; set; }
        public string HealthStatus { get; set; } = "Good";
    }

    public class ConnectionPoolStats
    {
        public int AvailableConnections { get; set; }
        public int TotalConnections { get; set; }
        public int QueueLength { get; set; }
        public double UtilizationRate { get; set; }
        public string Status { get; set; } = "Healthy";
        public DateTime LastUpdate { get; set; }
    }

    public class DatabaseQueueMonitor : INotifyPropertyChanged
    {
        private static DatabaseQueueMonitor? _instance;
        private readonly Timer _monitoringTimer;
        private readonly object _statsLock = new object();

        private DatabaseQueueStats _efCoreStats = new();
        private ConnectionPoolStats _poolStats = new();
        private bool _isMonitoring = false;

        // Queue tracking
        private readonly ConcurrentQueue<DateTime> _queryStartTimes = new();
        private readonly ConcurrentQueue<double> _executionTimes = new();
        private int _maxExecutionTimesSamples = 100;

        public static DatabaseQueueMonitor Instance => _instance ??= new DatabaseQueueMonitor();

        public DatabaseQueueStats EfCoreStats
        {
            get => _efCoreStats;
            private set
            {
                _efCoreStats = value;
                OnPropertyChanged();
            }
        }

        public ConnectionPoolStats PoolStats
        {
            get => _poolStats;
            private set
            {
                _poolStats = value;
                OnPropertyChanged();
            }
        }

        public bool IsMonitoring
        {
            get => _isMonitoring;
            set
            {
                if (_isMonitoring != value)
                {
                    _isMonitoring = value;
                    OnPropertyChanged();
                }
            }
        }

        private DatabaseQueueMonitor()
        {
            // Khởi tạo timer để monitor mỗi 2 giây
            _monitoringTimer = new Timer(MonitorCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
            IsMonitoring = true;
        }

        private void MonitorCallback(object? state)
        {
            if (!IsMonitoring) return;

            try
            {
                lock (_statsLock)
                {
                    UpdateEfCoreStats();
                    UpdateConnectionPoolStats();
                }

                // Cập nhật UI trên UI thread
                Dispatcher.UIThread.Post(() =>
                {
                    OnPropertyChanged(nameof(EfCoreStats));
                    OnPropertyChanged(nameof(PoolStats));
                });
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong DatabaseQueueMonitor: {ex.Message}");
            }
        }

        private void UpdateEfCoreStats()
        {
            // Lấy stats từ EFCoreQueueMonitor
            var efCoreMonitor = EFCoreQueueMonitor.Instance;
            var queryStats = efCoreMonitor.QueryStats;

            var newStats = new DatabaseQueueStats
            {
                PendingQueries = queryStats.PendingQueries,
                ExecutingQueries = queryStats.ExecutingQueries,
                CompletedQueries = queryStats.CompletedQueries,
                FailedQueries = queryStats.FailedQueries,
                AverageExecutionTimeMs = queryStats.AverageExecutionTimeMs,
                HealthStatus = queryStats.HealthStatus,
                LastUpdate = DateTime.Now
            };

            EfCoreStats = newStats;
        }

        private void UpdateConnectionPoolStats()
        {
            try
            {
                // Lấy stats từ EFCoreQueueMonitor thay vì OfflinePlayerConnectionPool
                var efCoreMonitor = EFCoreQueueMonitor.Instance;
                var connectionStats = efCoreMonitor.ConnectionStats;

                var newStats = new ConnectionPoolStats
                {
                    AvailableConnections = connectionStats.IdleConnections,
                    TotalConnections = connectionStats.TotalConnections,
                    QueueLength = connectionStats.PendingRequests,
                    UtilizationRate = connectionStats.UtilizationRate,
                    Status = connectionStats.Status,
                    LastUpdate = DateTime.Now
                };

                PoolStats = newStats;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi update connection pool stats: {ex.Message}");

                // Fallback stats
                PoolStats = new ConnectionPoolStats
                {
                    AvailableConnections = 0,
                    TotalConnections = 0,
                    QueueLength = 0,
                    UtilizationRate = 0,
                    Status = "Error",
                    LastUpdate = DateTime.Now
                };
            }
        }

        private int GetPendingQueriesCount()
        {
            // TODO: Implement actual EF Core pending queries count
            // For now, simulate based on queue start times
            var now = DateTime.Now;
            var pendingCount = 0;

            foreach (var startTime in _queryStartTimes)
            {
                if (now.Subtract(startTime).TotalSeconds < 30) // Consider queries pending if started within 30 seconds
                {
                    pendingCount++;
                }
            }

            return Math.Min(pendingCount, 50); // Cap at 50 for simulation
        }

        private int GetExecutingQueriesCount()
        {
            // TODO: Implement actual EF Core executing queries count
            // For now, simulate
            var random = new Random();
            return random.Next(0, Math.Min(5, _efCoreStats.PendingQueries));
        }

        private double CalculateAverageExecutionTime()
        {
            if (_executionTimes.IsEmpty)
                return 0;

            var sum = 0.0;
            var count = 0;

            foreach (var time in _executionTimes)
            {
                sum += time;
                count++;
            }

            return count > 0 ? sum / count : 0;
        }

        private string DetermineEfCoreHealth(DatabaseQueueStats stats)
        {
            if (stats.PendingQueries > 100 || stats.FailedQueries > 20)
                return "Critical";

            if (stats.PendingQueries > 50 || stats.FailedQueries > 10 || stats.AverageExecutionTimeMs > 1000)
                return "Warning";

            return "Good";
        }

        private string DeterminePoolStatus(ConnectionPoolStats stats)
        {
            if (stats.UtilizationRate > 95 || stats.TotalConnections == 0)
                return "Critical";

            if (stats.UtilizationRate > 80 || stats.TotalConnections < 5)
                return "Warning";

            return "Healthy";
        }

        // Public methods for tracking
        public void RecordQueryStart()
        {
            _queryStartTimes.Enqueue(DateTime.Now);

            // Keep only recent entries
            while (_queryStartTimes.Count > 1000)
            {
                _queryStartTimes.TryDequeue(out _);
            }
        }

        public void RecordQueryCompletion(double executionTimeMs)
        {
            _executionTimes.Enqueue(executionTimeMs);

            // Keep only recent samples
            while (_executionTimes.Count > _maxExecutionTimesSamples)
            {
                _executionTimes.TryDequeue(out _);
            }

            lock (_statsLock)
            {
                _efCoreStats.CompletedQueries++;
            }
        }

        public void RecordQueryFailure()
        {
            lock (_statsLock)
            {
                _efCoreStats.FailedQueries++;
            }
        }

        public void ResetCounters()
        {
            lock (_statsLock)
            {
                _efCoreStats.CompletedQueries = 0;
                _efCoreStats.FailedQueries = 0;

                // Clear queues
                while (_queryStartTimes.TryDequeue(out _)) { }
                while (_executionTimes.TryDequeue(out _)) { }
            }
        }

        public void StartMonitoring()
        {
            IsMonitoring = true;
        }

        public void StopMonitoring()
        {
            IsMonitoring = false;
        }

        public void Dispose()
        {
            _monitoringTimer?.Dispose();
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
