using System;
using System.Data.Common;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace HeroYulgang.Services
{
    /// <summary>
    /// Interceptor để monitor database queries cho EF Core
    /// </summary>
    public class DatabaseQueryInterceptor : DbCommandInterceptor
    {
        private readonly EFCoreQueueMonitor _monitor;

        public DatabaseQueryInterceptor()
        {
            _monitor = EFCoreQueueMonitor.Instance;
        }

        public override InterceptionResult<DbDataReader> ReaderExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result)
        {
            var queryId = _monitor.StartQuery(command.CommandText);
            eventData.Context?.ChangeTracker.Entries(); // Trigger để có context
            
            // Store queryId in command for later use
            command.CommandText = $"/* QueryId: {queryId} */ {command.CommandText}";
            
            return base.ReaderExecuting(command, eventData, result);
        }

        public override async ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = default)
        {
            var queryId = _monitor.StartQuery(command.CommandText);
            
            // Store queryId in command for later use
            command.CommandText = $"/* QueryId: {queryId} */ {command.CommandText}";
            
            return await base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override DbDataReader ReaderExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            DbDataReader result)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                var executionTime = eventData.Duration.TotalMilliseconds;
                _monitor.CompleteQuery(queryId, executionTime);
            }

            return base.ReaderExecuted(command, eventData, result);
        }

        public override async ValueTask<DbDataReader> ReaderExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            DbDataReader result,
            CancellationToken cancellationToken = default)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                var executionTime = eventData.Duration.TotalMilliseconds;
                _monitor.CompleteQuery(queryId, executionTime);
            }

            return await base.ReaderExecutedAsync(command, eventData, result, cancellationToken);
        }

        public override void CommandFailed(
            DbCommand command,
            CommandErrorEventData eventData)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                _monitor.FailQuery(queryId);
            }

            base.CommandFailed(command, eventData);
        }

        public override async Task CommandFailedAsync(
            DbCommand command,
            CommandErrorEventData eventData,
            CancellationToken cancellationToken = default)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                _monitor.FailQuery(queryId);
            }

            await base.CommandFailedAsync(command, eventData, cancellationToken);
        }

        // Scalar queries
        public override InterceptionResult<object> ScalarExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result)
        {
            var queryId = _monitor.StartQuery(command.CommandText);
            command.CommandText = $"/* QueryId: {queryId} */ {command.CommandText}";
            
            return base.ScalarExecuting(command, eventData, result);
        }

        public override async ValueTask<InterceptionResult<object>> ScalarExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result,
            CancellationToken cancellationToken = default)
        {
            var queryId = _monitor.StartQuery(command.CommandText);
            command.CommandText = $"/* QueryId: {queryId} */ {command.CommandText}";
            
            return await base.ScalarExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override object ScalarExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            object result)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                var executionTime = eventData.Duration.TotalMilliseconds;
                _monitor.CompleteQuery(queryId, executionTime);
            }

            return base.ScalarExecuted(command, eventData, result);
        }

        public override async ValueTask<object> ScalarExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            object result,
            CancellationToken cancellationToken = default)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                var executionTime = eventData.Duration.TotalMilliseconds;
                _monitor.CompleteQuery(queryId, executionTime);
            }

            return await base.ScalarExecutedAsync(command, eventData, result, cancellationToken);
        }

        // NonQuery commands
        public override InterceptionResult<int> NonQueryExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result)
        {
            var queryId = _monitor.StartQuery(command.CommandText);
            command.CommandText = $"/* QueryId: {queryId} */ {command.CommandText}";
            
            return base.NonQueryExecuting(command, eventData, result);
        }

        public override async ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            var queryId = _monitor.StartQuery(command.CommandText);
            command.CommandText = $"/* QueryId: {queryId} */ {command.CommandText}";
            
            return await base.NonQueryExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override int NonQueryExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            int result)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                var executionTime = eventData.Duration.TotalMilliseconds;
                _monitor.CompleteQuery(queryId, executionTime);
            }

            return base.NonQueryExecuted(command, eventData, result);
        }

        public override async ValueTask<int> NonQueryExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            int result,
            CancellationToken cancellationToken = default)
        {
            var queryId = ExtractQueryId(command.CommandText);
            if (!string.IsNullOrEmpty(queryId))
            {
                var executionTime = eventData.Duration.TotalMilliseconds;
                _monitor.CompleteQuery(queryId, executionTime);
            }

            return await base.NonQueryExecutedAsync(command, eventData, result, cancellationToken);
        }

        /// <summary>
        /// Extract QueryId từ command text
        /// </summary>
        private string ExtractQueryId(string commandText)
        {
            try
            {
                if (string.IsNullOrEmpty(commandText))
                    return string.Empty;

                var startIndex = commandText.IndexOf("/* QueryId: ");
                if (startIndex == -1)
                    return string.Empty;

                startIndex += 12; // Length of "/* QueryId: "
                var endIndex = commandText.IndexOf(" */", startIndex);
                if (endIndex == -1)
                    return string.Empty;

                return commandText.Substring(startIndex, endIndex - startIndex);
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
