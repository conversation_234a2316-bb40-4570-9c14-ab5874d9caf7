using System;
using System.Threading;
using System.Threading.Tasks;

namespace HeroYulgang.Services
{
    /// <summary>
    /// Service an toàn để cung cấp database metrics cho game server
    /// Tr<PERSON><PERSON> việ<PERSON> sử dụng trực tiếp DatabaseManager.Instance trong game server
    /// </summary>
    public class DatabaseMetricsService
    {
        private static DatabaseMetricsService? _instance;
        private readonly Timer _metricsTimer;
        private readonly object _metricsLock = new object();
        
        // Cached metrics để tránh frequent access
        private DatabaseMetrics _cachedMetrics = new();
        private DateTime _lastUpdate = DateTime.MinValue;
        private readonly TimeSpan _cacheTimeout = TimeSpan.FromSeconds(5);

        public static DatabaseMetricsService Instance => _instance ??= new DatabaseMetricsService();

        public DatabaseMetrics CurrentMetrics
        {
            get
            {
                lock (_metricsLock)
                {
                    // Return cached metrics if still valid
                    if (DateTime.Now - _lastUpdate < _cacheTimeout)
                    {
                        return _cachedMetrics;
                    }
                    
                    // Update metrics if cache expired
                    UpdateMetrics();
                    return _cachedMetrics;
                }
            }
        }

        private DatabaseMetricsService()
        {
            // Update metrics every 5 seconds
            _metricsTimer = new Timer(UpdateMetricsCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
        }

        private void UpdateMetricsCallback(object? state)
        {
            try
            {
                lock (_metricsLock)
                {
                    UpdateMetrics();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi update database metrics: {ex.Message}");
            }
        }

        private void UpdateMetrics()
        {
            try
            {
                // Safely get metrics from monitors
                var efCoreStats = EFCoreQueueMonitor.Instance.QueryStats;
                var connectionStats = EFCoreQueueMonitor.Instance.ConnectionStats;

                _cachedMetrics = new DatabaseMetrics
                {
                    // Query metrics
                    PendingQueries = efCoreStats.PendingQueries,
                    ExecutingQueries = efCoreStats.ExecutingQueries,
                    CompletedQueries = efCoreStats.CompletedQueries,
                    FailedQueries = efCoreStats.FailedQueries,
                    AverageExecutionTimeMs = efCoreStats.AverageExecutionTimeMs,
                    QueryHealthStatus = efCoreStats.HealthStatus,
                    
                    // Connection metrics
                    TotalConnections = connectionStats.TotalConnections,
                    ActiveConnections = connectionStats.ActiveConnections,
                    IdleConnections = connectionStats.IdleConnections,
                    PendingRequests = connectionStats.PendingRequests,
                    ConnectionUtilizationRate = connectionStats.UtilizationRate,
                    ConnectionStatus = connectionStats.Status,
                    
                    // Metadata
                    LastUpdate = DateTime.Now,
                    IsHealthy = DetermineOverallHealth(efCoreStats.HealthStatus, connectionStats.Status)
                };

                _lastUpdate = DateTime.Now;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong UpdateMetrics: {ex.Message}");
                
                // Fallback to safe defaults
                _cachedMetrics = new DatabaseMetrics
                {
                    LastUpdate = DateTime.Now,
                    IsHealthy = false,
                    QueryHealthStatus = "Error",
                    ConnectionStatus = "Error"
                };
            }
        }

        private bool DetermineOverallHealth(string queryHealth, string connectionStatus)
        {
            return queryHealth != "Critical" && 
                   queryHealth != "Error" && 
                   connectionStatus != "Critical" && 
                   connectionStatus != "Error";
        }

        /// <summary>
        /// Lấy metrics cho game server một cách an toàn
        /// </summary>
        public DatabaseMetrics GetMetricsForGameServer()
        {
            try
            {
                return CurrentMetrics;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi get metrics for game server: {ex.Message}");
                
                // Return safe fallback
                return new DatabaseMetrics
                {
                    LastUpdate = DateTime.Now,
                    IsHealthy = false,
                    QueryHealthStatus = "Error",
                    ConnectionStatus = "Error"
                };
            }
        }

        /// <summary>
        /// Kiểm tra xem database có healthy không
        /// </summary>
        public bool IsDatabaseHealthy()
        {
            try
            {
                return CurrentMetrics.IsHealthy;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Lấy số lượng queries đang pending
        /// </summary>
        public int GetPendingQueriesCount()
        {
            try
            {
                return CurrentMetrics.PendingQueries;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Lấy connection utilization rate
        /// </summary>
        public double GetConnectionUtilizationRate()
        {
            try
            {
                return CurrentMetrics.ConnectionUtilizationRate;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Async method để lấy metrics (cho game server sử dụng)
        /// </summary>
        public async Task<DatabaseMetrics> GetMetricsAsync()
        {
            return await Task.Run(() => GetMetricsForGameServer());
        }

        public void Dispose()
        {
            _metricsTimer?.Dispose();
        }
    }

    /// <summary>
    /// DTO chứa database metrics
    /// </summary>
    public class DatabaseMetrics
    {
        // Query metrics
        public int PendingQueries { get; set; }
        public int ExecutingQueries { get; set; }
        public int CompletedQueries { get; set; }
        public int FailedQueries { get; set; }
        public double AverageExecutionTimeMs { get; set; }
        public string QueryHealthStatus { get; set; } = "Unknown";
        
        // Connection metrics
        public int TotalConnections { get; set; }
        public int ActiveConnections { get; set; }
        public int IdleConnections { get; set; }
        public int PendingRequests { get; set; }
        public double ConnectionUtilizationRate { get; set; }
        public string ConnectionStatus { get; set; } = "Unknown";
        
        // Overall status
        public bool IsHealthy { get; set; }
        public DateTime LastUpdate { get; set; }
        
        /// <summary>
        /// Tạo summary string cho logging
        /// </summary>
        public string ToSummaryString()
        {
            return $"DB Health: {(IsHealthy ? "OK" : "ISSUE")} | " +
                   $"Queries: P={PendingQueries}, E={ExecutingQueries}, F={FailedQueries} | " +
                   $"Connections: {ActiveConnections}/{TotalConnections} ({ConnectionUtilizationRate:F1}%) | " +
                   $"Avg Time: {AverageExecutionTimeMs:F2}ms";
        }
    }
}
