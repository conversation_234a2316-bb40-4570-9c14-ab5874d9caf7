using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace HeroYulgang.Services
{
    public enum QueueIssueType
    {
        HighPendingQueries,
        SlowExecutionTime,
        HighFailureRate,
        ConnectionPoolExhaustion,
        MemoryLeak,
        DeadlockDetected,
        PerformanceDegradation
    }

    public class QueueIssue
    {
        public QueueIssueType Type { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Severity { get; set; } = "Medium"; // Low, Medium, High, Critical
        public List<string> Recommendations { get; set; } = new();
        public DateTime DetectedAt { get; set; } = DateTime.Now;
        public bool IsResolved { get; set; } = false;
    }

    public class DatabaseQueueAnalyzer
    {
        private static DatabaseQueueAnalyzer? _instance;
        private readonly List<QueueIssue> _detectedIssues = new();
        private readonly object _analysisLock = new object();

        public static DatabaseQueueAnalyzer Instance => _instance ??= new DatabaseQueueAnalyzer();

        public List<QueueIssue> DetectedIssues
        {
            get
            {
                lock (_analysisLock)
                {
                    return _detectedIssues.ToList();
                }
            }
        }

        private DatabaseQueueAnalyzer() { }

        /// <summary>
        /// Phân tích tình trạng database queue và đưa ra khuyến nghị
        /// </summary>
        public List<QueueIssue> AnalyzeQueueStatus()
        {
            lock (_analysisLock)
            {
                _detectedIssues.Clear();

                var efCoreStats = DatabaseQueueMonitor.Instance.EfCoreStats;
                var poolStats = DatabaseQueueMonitor.Instance.PoolStats;

                // Phân tích EF Core Queue
                AnalyzeEfCoreQueue(efCoreStats);

                // Phân tích Connection Pool
                AnalyzeConnectionPool(poolStats);

                // Phân tích tổng thể
                AnalyzeOverallPerformance(efCoreStats, poolStats);

                return _detectedIssues.ToList();
            }
        }

        private void AnalyzeEfCoreQueue(DatabaseQueueStats stats)
        {
            // High Pending Queries
            if (stats.PendingQueries > 100)
            {
                _detectedIssues.Add(new QueueIssue
                {
                    Type = QueueIssueType.HighPendingQueries,
                    Title = "Số lượng queries đang chờ quá cao",
                    Description = $"Hiện tại có {stats.PendingQueries} queries đang chờ xử lý, vượt ngưỡng an toàn (100).",
                    Severity = stats.PendingQueries > 200 ? "Critical" : "High",
                    Recommendations = new List<string>
                    {
                        "Tăng số lượng connection pool",
                        "Tối ưu hóa các queries chậm",
                        "Implement query batching",
                        "Xem xét sử dụng async/await cho database operations",
                        "Kiểm tra deadlocks và long-running transactions"
                    }
                });
            }

            // Slow Execution Time
            if (stats.AverageExecutionTimeMs > 1000)
            {
                _detectedIssues.Add(new QueueIssue
                {
                    Type = QueueIssueType.SlowExecutionTime,
                    Title = "Thời gian thực thi queries quá chậm",
                    Description = $"Thời gian thực thi trung bình là {stats.AverageExecutionTimeMs:F2}ms, vượt ngưỡng khuyến nghị (1000ms).",
                    Severity = stats.AverageExecutionTimeMs > 5000 ? "Critical" : "High",
                    Recommendations = new List<string>
                    {
                        "Thêm indexes cho các bảng thường xuyên query",
                        "Tối ưu hóa SQL queries (tránh SELECT *, sử dụng WHERE hiệu quả)",
                        "Implement query caching",
                        "Phân tích execution plans",
                        "Xem xét database partitioning cho bảng lớn",
                        "Sử dụng stored procedures cho complex queries"
                    }
                });
            }

            // High Failure Rate
            if (stats.FailedQueries > 20)
            {
                _detectedIssues.Add(new QueueIssue
                {
                    Type = QueueIssueType.HighFailureRate,
                    Title = "Tỷ lệ queries thất bại cao",
                    Description = $"Có {stats.FailedQueries} queries thất bại, cần kiểm tra nguyên nhân.",
                    Severity = stats.FailedQueries > 50 ? "Critical" : "High",
                    Recommendations = new List<string>
                    {
                        "Kiểm tra database connection stability",
                        "Review error logs để xác định nguyên nhân",
                        "Implement retry logic với exponential backoff",
                        "Kiểm tra database server resources (CPU, Memory, Disk)",
                        "Validate input data trước khi execute queries",
                        "Implement circuit breaker pattern"
                    }
                });
            }
        }

        private void AnalyzeConnectionPool(ConnectionPoolStats stats)
        {
            // Connection Pool Exhaustion
            if (stats.UtilizationRate > 90)
            {
                _detectedIssues.Add(new QueueIssue
                {
                    Type = QueueIssueType.ConnectionPoolExhaustion,
                    Title = "Connection pool gần cạn kiệt",
                    Description = $"Tỷ lệ sử dụng connection pool là {stats.UtilizationRate:F1}%, rất gần giới hạn.",
                    Severity = stats.UtilizationRate > 95 ? "Critical" : "High",
                    Recommendations = new List<string>
                    {
                        "Tăng MaxPoolSize trong connection string",
                        "Implement connection pooling optimization",
                        "Giảm connection timeout để release connections nhanh hơn",
                        "Sử dụng using statements để đảm bảo dispose connections",
                        "Implement connection monitoring và alerting",
                        "Xem xét sử dụng multiple connection pools cho different operations"
                    }
                });
            }

            // Low Total Connections
            if (stats.TotalConnections < 10)
            {
                _detectedIssues.Add(new QueueIssue
                {
                    Type = QueueIssueType.ConnectionPoolExhaustion,
                    Title = "Số lượng connections quá thấp",
                    Description = $"Chỉ có {stats.TotalConnections} connections, có thể không đủ cho load hiện tại.",
                    Severity = "Medium",
                    Recommendations = new List<string>
                    {
                        "Tăng MinPoolSize và MaxPoolSize",
                        "Monitor concurrent user load",
                        "Implement connection pre-warming",
                        "Xem xét database server capacity"
                    }
                });
            }
        }

        private void AnalyzeOverallPerformance(DatabaseQueueStats efCoreStats, ConnectionPoolStats poolStats)
        {
            // Performance Degradation Detection
            var totalIssues = _detectedIssues.Count;
            var criticalIssues = _detectedIssues.Count(i => i.Severity == "Critical");
            var highIssues = _detectedIssues.Count(i => i.Severity == "High");

            if (criticalIssues > 0 || highIssues > 2)
            {
                _detectedIssues.Add(new QueueIssue
                {
                    Type = QueueIssueType.PerformanceDegradation,
                    Title = "Hiệu suất database tổng thể đang suy giảm",
                    Description = $"Phát hiện {criticalIssues} vấn đề nghiêm trọng và {highIssues} vấn đề mức cao.",
                    Severity = criticalIssues > 0 ? "Critical" : "High",
                    Recommendations = new List<string>
                    {
                        "Thực hiện database maintenance (update statistics, rebuild indexes)",
                        "Monitor database server resources",
                        "Implement comprehensive logging và monitoring",
                        "Xem xét scale-out database architecture",
                        "Backup và recovery strategy review",
                        "Performance tuning consultation với DBA"
                    }
                });
            }
        }

        /// <summary>
        /// Tạo báo cáo chi tiết về tình trạng database queue
        /// </summary>
        public string GenerateDetailedReport()
        {
            var issues = AnalyzeQueueStatus();
            var report = new StringBuilder();

            report.AppendLine("=== BÁO CÁO PHÂN TÍCH DATABASE QUEUE ===");
            report.AppendLine($"Thời gian: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            var efCoreStats = DatabaseQueueMonitor.Instance.EfCoreStats;
            var poolStats = DatabaseQueueMonitor.Instance.PoolStats;

            // Current Status
            report.AppendLine("=== TÌNH TRẠNG HIỆN TẠI ===");
            report.AppendLine($"EF Core Queue:");
            report.AppendLine($"  - Pending Queries: {efCoreStats.PendingQueries}");
            report.AppendLine($"  - Executing Queries: {efCoreStats.ExecutingQueries}");
            report.AppendLine($"  - Completed Queries: {efCoreStats.CompletedQueries}");
            report.AppendLine($"  - Failed Queries: {efCoreStats.FailedQueries}");
            report.AppendLine($"  - Average Execution Time: {efCoreStats.AverageExecutionTimeMs:F2}ms");
            report.AppendLine($"  - Health Status: {efCoreStats.HealthStatus}");
            report.AppendLine();

            report.AppendLine($"Connection Pool:");
            report.AppendLine($"  - Available Connections: {poolStats.AvailableConnections}");
            report.AppendLine($"  - Total Connections: {poolStats.TotalConnections}");
            report.AppendLine($"  - Queue Length: {poolStats.QueueLength}");
            report.AppendLine($"  - Utilization Rate: {poolStats.UtilizationRate:F1}%");
            report.AppendLine($"  - Status: {poolStats.Status}");
            report.AppendLine();

            // Issues and Recommendations
            if (issues.Any())
            {
                report.AppendLine("=== VẤN ĐỀ PHÁT HIỆN ===");
                foreach (var issue in issues.OrderByDescending(i => GetSeverityWeight(i.Severity)))
                {
                    report.AppendLine($"[{issue.Severity.ToUpper()}] {issue.Title}");
                    report.AppendLine($"Mô tả: {issue.Description}");
                    report.AppendLine("Khuyến nghị:");
                    foreach (var recommendation in issue.Recommendations)
                    {
                        report.AppendLine($"  - {recommendation}");
                    }
                    report.AppendLine();
                }
            }
            else
            {
                report.AppendLine("=== KHÔNG PHÁT HIỆN VẤN ĐỀ ===");
                report.AppendLine("Database queue đang hoạt động bình thường.");
                report.AppendLine();
            }

            // General Recommendations
            report.AppendLine("=== KHUYẾN NGHỊ CHUNG ===");
            report.AppendLine("- Thường xuyên monitor database performance");
            report.AppendLine("- Implement automated alerting cho critical thresholds");
            report.AppendLine("- Backup database thường xuyên");
            report.AppendLine("- Review và optimize queries định kỳ");
            report.AppendLine("- Keep database statistics up to date");

            return report.ToString();
        }

        private int GetSeverityWeight(string severity)
        {
            return severity switch
            {
                "Critical" => 4,
                "High" => 3,
                "Medium" => 2,
                "Low" => 1,
                _ => 0
            };
        }

        /// <summary>
        /// Đánh dấu issue đã được giải quyết
        /// </summary>
        public void ResolveIssue(QueueIssueType issueType)
        {
            lock (_analysisLock)
            {
                var issue = _detectedIssues.FirstOrDefault(i => i.Type == issueType);
                if (issue != null)
                {
                    issue.IsResolved = true;
                }
            }
        }

        /// <summary>
        /// Xóa tất cả issues đã được giải quyết
        /// </summary>
        public void ClearResolvedIssues()
        {
            lock (_analysisLock)
            {
                _detectedIssues.RemoveAll(i => i.IsResolved);
            }
        }
    }
}
