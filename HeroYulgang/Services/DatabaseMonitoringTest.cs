using System;
using System.Threading.Tasks;
using HeroYulgang.Extensions;

namespace HeroYulgang.Services
{
    /// <summary>
    /// Test class để verify database monitoring system
    /// </summary>
    public static class DatabaseMonitoringTest
    {
        /// <summary>
        /// Test toàn bộ hệ thống database monitoring
        /// </summary>
        public static async Task RunFullTest()
        {
            Logger.Instance.Info("=== Bắt đầu test Database Monitoring System ===");

            try
            {
                // Test 1: EFCoreQueueMonitor
                await TestEFCoreQueueMonitor();

                // Test 2: DatabaseMetricsService
                await TestDatabaseMetricsService();

                // Test 3: DatabaseExtensions
                await TestDatabaseExtensions();

                // Test 4: DatabaseQueueAnalyzer
                await TestDatabaseQueueAnalyzer();

                Logger.Instance.Info("=== Hoàn thành test Database Monitoring System - TẤT CẢ PASS ===");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Test failed: {ex.Message}");
                throw;
            }
        }

        private static async Task TestEFCoreQueueMonitor()
        {
            Logger.Instance.Info("Testing EFCoreQueueMonitor...");

            var monitor = EFCoreQueueMonitor.Instance;
            
            // Test monitoring state
            if (!monitor.IsMonitoring)
            {
                throw new Exception("EFCoreQueueMonitor should be monitoring by default");
            }

            // Test query tracking
            var queryId = monitor.StartQuery("Test Query");
            await Task.Delay(100);
            monitor.CompleteQuery(queryId, 50.0);

            // Test stats
            var queryStats = monitor.QueryStats;
            var connectionStats = monitor.ConnectionStats;

            if (queryStats == null || connectionStats == null)
            {
                throw new Exception("Stats should not be null");
            }

            Logger.Instance.Info($"EFCore Query Stats: Pending={queryStats.PendingQueries}, Completed={queryStats.CompletedQueries}");
            Logger.Instance.Info($"EFCore Connection Stats: Total={connectionStats.TotalConnections}, Active={connectionStats.ActiveConnections}");
            Logger.Instance.Info("✓ EFCoreQueueMonitor test passed");
        }

        private static async Task TestDatabaseMetricsService()
        {
            Logger.Instance.Info("Testing DatabaseMetricsService...");

            var service = DatabaseMetricsService.Instance;
            
            // Test metrics retrieval
            var metrics = service.GetMetricsForGameServer();
            if (metrics == null)
            {
                throw new Exception("Metrics should not be null");
            }

            // Test async metrics
            var asyncMetrics = await service.GetMetricsAsync();
            if (asyncMetrics == null)
            {
                throw new Exception("Async metrics should not be null");
            }

            // Test health check
            var isHealthy = service.IsDatabaseHealthy();
            Logger.Instance.Info($"Database Health: {isHealthy}");

            // Test summary
            var summary = metrics.ToSummaryString();
            Logger.Instance.Info($"Metrics Summary: {summary}");

            Logger.Instance.Info("✓ DatabaseMetricsService test passed");
        }

        private static async Task TestDatabaseExtensions()
        {
            Logger.Instance.Info("Testing DatabaseExtensions...");

            // Test health check
            var isHealthy = DatabaseExtensions.IsDatabaseHealthy();
            Logger.Instance.Info($"Database Healthy (Extensions): {isHealthy}");

            // Test summary
            var summary = DatabaseExtensions.GetDatabaseSummary();
            Logger.Instance.Info($"Database Summary: {summary}");

            // Test operation check
            var shouldExecute = DatabaseExtensions.ShouldExecuteDatabaseOperation();
            Logger.Instance.Info($"Should Execute DB Operation: {shouldExecute}");

            // Test delay recommendation
            var delay = DatabaseExtensions.GetRecommendedDelayMs();
            Logger.Instance.Info($"Recommended Delay: {delay}ms");

            // Test performance grade
            var grade = DatabaseExtensions.GetDatabasePerformanceGrade();
            Logger.Instance.Info($"Performance Grade: {grade}");

            // Test alert check
            var shouldAlert = DatabaseExtensions.ShouldAlertDatabasePerformance();
            Logger.Instance.Info($"Should Alert: {shouldAlert}");

            // Test retry mechanism
            var result = await DatabaseExtensions.ExecuteWithRetryAsync(async () =>
            {
                await Task.Delay(10);
                return "Test Success";
            });

            if (result != "Test Success")
            {
                throw new Exception("Retry mechanism failed");
            }

            Logger.Instance.Info("✓ DatabaseExtensions test passed");
        }

        private static async Task TestDatabaseQueueAnalyzer()
        {
            Logger.Instance.Info("Testing DatabaseQueueAnalyzer...");

            var analyzer = DatabaseQueueAnalyzer.Instance;
            
            // Test analysis
            var issues = analyzer.AnalyzeQueueStatus();
            Logger.Instance.Info($"Detected Issues: {issues.Count}");

            foreach (var issue in issues)
            {
                Logger.Instance.Info($"Issue: [{issue.Severity}] {issue.Title}");
            }

            // Test report generation
            var report = analyzer.GenerateDetailedReport();
            if (string.IsNullOrEmpty(report))
            {
                throw new Exception("Report should not be empty");
            }

            Logger.Instance.Info($"Generated report length: {report.Length} characters");
            Logger.Instance.Info("✓ DatabaseQueueAnalyzer test passed");

            await Task.CompletedTask;
        }

        /// <summary>
        /// Test performance với simulated load
        /// </summary>
        public static async Task RunPerformanceTest(int queryCount = 100)
        {
            Logger.Instance.Info($"=== Bắt đầu Performance Test với {queryCount} queries ===");

            var monitor = EFCoreQueueMonitor.Instance;
            var startTime = DateTime.Now;

            // Simulate multiple queries
            var tasks = new Task[queryCount];
            for (int i = 0; i < queryCount; i++)
            {
                tasks[i] = SimulateQuery(monitor, i);
            }

            await Task.WhenAll(tasks);

            var endTime = DateTime.Now;
            var duration = endTime - startTime;

            Logger.Instance.Info($"Performance Test hoàn thành trong {duration.TotalMilliseconds:F2}ms");

            // Check final stats
            var queryStats = monitor.QueryStats;
            Logger.Instance.Info($"Final Stats: Completed={queryStats.CompletedQueries}, Failed={queryStats.FailedQueries}, Avg={queryStats.AverageExecutionTimeMs:F2}ms");

            Logger.Instance.Info("=== Performance Test hoàn thành ===");
        }

        private static async Task SimulateQuery(EFCoreQueueMonitor monitor, int queryIndex)
        {
            var queryId = monitor.StartQuery($"Test Query {queryIndex}");
            
            // Simulate random execution time
            var random = new Random();
            var executionTime = random.Next(10, 200);
            await Task.Delay(executionTime);

            // Simulate occasional failures
            if (random.Next(100) < 5) // 5% failure rate
            {
                monitor.FailQuery(queryId);
            }
            else
            {
                monitor.CompleteQuery(queryId, executionTime);
            }
        }

        /// <summary>
        /// Test integration với UI components
        /// </summary>
        public static void TestUIIntegration()
        {
            Logger.Instance.Info("=== Testing UI Integration ===");

            try
            {
                // Test DatabaseQueueMonitor
                var queueMonitor = DatabaseQueueMonitor.Instance;
                var efCoreStats = queueMonitor.EfCoreStats;
                var poolStats = queueMonitor.PoolStats;

                Logger.Instance.Info($"UI Integration - EF Core: {efCoreStats.HealthStatus}, Pool: {poolStats.Status}");

                // Test metrics for UI
                var metrics = DatabaseMetricsService.Instance.CurrentMetrics;
                Logger.Instance.Info($"UI Metrics: Health={metrics.IsHealthy}, Queries={metrics.PendingQueries}");

                Logger.Instance.Info("✓ UI Integration test passed");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"UI Integration test failed: {ex.Message}");
                throw;
            }
        }
    }
}
