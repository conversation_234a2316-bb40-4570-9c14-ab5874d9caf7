using System;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor x<PERSON> lý kết nối của một client cụ thể
    /// </summary>
    public class ClientActor : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private readonly IActorRef _packetHandlerActor;
        private Players _player;
        private ActorNetState _actorNetState;

        public ClientActor(IActorRef connection, ClientSession session, IActorRef packetHandlerActor)
        {
            _connection = connection;
            _session = session;
            _packetHandlerActor = packetHandlerActor;
            // World.list.Add(session.SessionId, this);

            // Tạo ActorNetState cho kết nối này
            _actorNetState = PlayerNetworkManager.CreateActorNetState(_connection, _session.SessionId, _session.RemoteEndPoint);

            // <PERSON><PERSON><PERSON> nghĩa các message handler
            Receive<Tcp.Received>(HandleReceived);
            Receive<Tcp.ConnectionClosed>(HandleConnectionClosed);
            Receive<SetPlayerReference>(SetPlayerReference);
            Receive<SetPlayerReferenceAndLogin>(SetPlayerReferenceAndLogin);
        }

        private void SetPlayerReference(SetPlayerReference message)
        {
            _player = message.Player;

            // Thiết lập player context cho PacketHandlerActor thay vì tạo PlayerPacketHandlerActor riêng
            if (_player != null && _actorNetState != null)
            {
                Logger.Instance.Debug($"Player ID {_player.AccountID} (SessionID: {_player.SessionID}) đã kết nối {_actorNetState.SessionID} ");
                // Cập nhật Client của Player để sử dụng ActorNetState
                PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                // Gửi player context đến PacketHandlerActor
                _packetHandlerActor.Tell(new SetPlayerContext(_player, _actorNetState, _connection));

                Logger.Instance.Debug($"Đã thiết lập player context cho PacketHandlerActor cho người chơi {_player.CharacterName}");
            }
        }

        private void SetPlayerReferenceAndLogin(SetPlayerReferenceAndLogin message)
        {
            try
            {
                _player = message.Player;

                // Thiết lập player context cho PacketHandlerActor
                if (_player != null && _actorNetState != null)
                {
                    // Đồng bộ SessionID giữa Player và ActorNetState - sử dụng reflection vì SessionID là read-only
                    var sessionIdField = typeof(PlayersBes).GetField("_sessionID", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (sessionIdField != null)
                    {
                        sessionIdField.SetValue(_player, _actorNetState.SessionID);
                    }
                    else
                    {
                        Logger.Instance.Warning("Không thể tìm thấy field _sessionID để cập nhật SessionID");
                    }

                    Logger.Instance.Debug($"Player ID {_player.AccountID} (SessionID: {_player.SessionID}) đã kết nối {_actorNetState.SessionID}");

                    // Kiểm tra xem player đã đăng nhập chưa
                    if (World.allConnectedChars.TryGetValue(_player.SessionID, out var existingPlayer))
                    {
                        Logger.Instance.Warning($"Player {message.Username} đã có trong World với SessionID {_player.SessionID}");

                        // Ngắt kết nối cũ nếu có
                        try
                        {
                            existingPlayer.Client?.Dispose();
                            World.allConnectedChars.Remove(_player.SessionID);
                            Logger.Instance.Debug($"Đã ngắt kết nối cũ cho player {message.Username}");
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Lỗi khi ngắt kết nối cũ: {ex.Message}");
                        }
                    }

                    // Cập nhật Client của Player để sử dụng ActorNetState
                    PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                    // Gửi player context đến PacketHandlerActor
                    _packetHandlerActor.Tell(new SetPlayerContext(_player, _actorNetState, _connection));

                    Logger.Instance.Debug($"Đã thiết lập player context cho PacketHandlerActor cho người chơi {_player.AccountID}");

                    // Xử lý đăng nhập
                    try
                    {
                        _player.KetNoi_DangNhap(message.LoginData, message.LoginData.Length);
                        Logger.Instance.Debug($"Người dùng {message.Username} đã gọi KetNoi_DangNhap thành công");
                        message.Session.IsAuthenticated = true;
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Lỗi khi xử lý đăng nhập cho {message.Username}: {ex.Message}");
                        Logger.Instance.Error($"Stack trace: {ex.StackTrace}");

                        // Ngắt kết nối nếu đăng nhập thất bại
                        _connection.Tell(Tcp.Close.Instance);
                    }
                }
                else
                {
                    Logger.Instance.Error($"SetPlayerReferenceAndLogin: _player hoặc _actorNetState là null");
                    _connection.Tell(Tcp.Close.Instance);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong SetPlayerReferenceAndLogin cho {message.Username}: {ex.Message}");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
                _connection.Tell(Tcp.Close.Instance);
            }
        }

        private void HandleReceived(Tcp.Received received)
        {
            try
            {
                // Cập nhật thời gian hoạt động
                _session.UpdateActivity();

                // Chuyển đổi ByteString thành byte array
                byte[] data = received.Data.ToArray();
                byte[] decryptedData = Utils.Crypto.DecryptPacket(data);

                // Ghi log gói tin nhận được nếu cần
                //PacketLogger.LogIncomingPacket(_session.SessionId, data);

                // Gửi packet đến PacketHandlerActor thống nhất
                if (_player != null)
                {
                    // Nếu đã có player, gửi ProcessPlayerPacket
                   // Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor (ProcessPlayerPacket) cho session {_session.SessionId}");

                    _packetHandlerActor.Tell(new ProcessPlayerPacket(decryptedData, decryptedData.Length));
                }
                else
                {
                    // Nếu chưa có player, gửi ProcessPacket thông thường
                   // Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor (ProcessPacket) cho session {_session.SessionId}");

                    _packetHandlerActor.Tell(new ProcessPacket(_connection, _session, decryptedData));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được từ client {_session.SessionId}: {ex.Message}");
            }
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            // Kết nối đã đóng, dừng actor
            Context.Stop(Self);
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player
    /// </summary>
    public class SetPlayerReference
    {
        public Players Player { get; }

        public SetPlayerReference(Players player)
        {
            Player = player;
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player và xử lý đăng nhập
    /// </summary>
    public class SetPlayerReferenceAndLogin
    {
        public Players Player { get; }
        public byte[] LoginData { get; }
        public ClientSession Session { get; }
        public string Username { get; }

        public SetPlayerReferenceAndLogin(Players player, byte[] loginData, ClientSession session, string username)
        {
            Player = player;
            LoginData = loginData;
            Session = session;
            Username = username;
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin
    /// </summary>
    public class ProcessPacket
    {
        public IActorRef Connection { get; }
        public ClientSession Session { get; }
        public byte[] Data { get; }

        public ProcessPacket(IActorRef connection, ClientSession session, byte[] data)
        {
            Connection = connection;
            Session = session;
            Data = data;
        }
    }
}
