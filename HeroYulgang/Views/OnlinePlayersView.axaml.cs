using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Timers;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Threading;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views
{
    public partial class OnlinePlayersView : UserControl
    {
        public ObservableCollection<Players> Players { get; set; }
        private Players? _selectedPlayer;

        private System.Timers.Timer _refreshTimer;
        private readonly object _refreshLock = new object();
        private bool _autoRefreshEnabled = true;
        private int _refreshInterval = 3000; // 3 seconds - reasonable interval

        public OnlinePlayersView()
        {
            InitializeComponent();
            Players = new ObservableCollection<Players>();
            SetupEventHandlers();
            SetupAutoRefresh();
            LoadPlayers();
        }

        private void SetupEventHandlers()
        {
            SearchButton.Click += OnSearchClick;
            RefreshButton.Click += OnRefreshClick;
            PlayersDataGrid.SelectionChanged += OnPlayerSelectionChanged;

            KickPlayerButton.Click += OnKickPlayerClick;
            SendMessageButton.Click += OnSendMessageClick;
            TeleportToPlayerButton.Click += OnTeleportToPlayerClick;
            ViewPlayerDetailsButton.Click += OnViewPlayerDetailsClick;
            ExportPlayersButton.Click += OnExportPlayersClick;
            BroadcastMessageButton.Click += OnBroadcastMessageClick;
        }

        /// <summary>
        /// Thiết lập auto-refresh cho danh sách players
        /// </summary>
        private void SetupAutoRefresh()
        {
            _refreshTimer = new System.Timers.Timer(_refreshInterval);
            _refreshTimer.Elapsed += OnAutoRefresh;
            _refreshTimer.AutoReset = true;
            _refreshTimer.Enabled = _autoRefreshEnabled;
        }

        /// <summary>
        /// Event handler cho auto-refresh
        /// </summary>
        private void OnAutoRefresh(object sender, ElapsedEventArgs e)
        {
            if (!_autoRefreshEnabled) return;

            try
            {
                // Sử dụng Dispatcher để update UI thread
                Dispatcher.UIThread.Post(() =>
                {
                    lock (_refreshLock)
                    {
                        LoadPlayersInternal();
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi auto-refresh players: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra xem danh sách players có thay đổi không
        /// </summary>
        private bool HasPlayersChanged(List<Players> newPlayers)
        {
            if (Players.Count != newPlayers.Count)
                return true;

            var currentPlayerIds = Players.Select(p => p.SessionID).ToHashSet();
            var newPlayerIds = newPlayers.Select(p => p.SessionID).ToHashSet();

            return !currentPlayerIds.SetEquals(newPlayerIds);
        }

        /// <summary>
        /// Load danh sách players internal (không update UI count)
        /// </summary>
        private void LoadPlayersInternal()
        {
            try
            {
                var currentPlayers = World.allConnectedChars.Values.ToList();

                // Kiểm tra xem có thay đổi không
                if (HasPlayersChanged(currentPlayers))
                {
                    Players.Clear();

                    foreach (var player in currentPlayers)
                    {
                        if (player != null)
                        {
                            Players.Add(player);
                        }
                    }

                    PlayersDataGrid.ItemsSource = Players;
                    UpdatePlayerCount();
                    Logger.Instance.Debug($"Auto-refresh: Đã cập nhật {Players.Count} players");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi LoadPlayersInternal: {ex.Message}");
            }
        }

        private void LoadPlayers()
        {
            try
            {
                LoadPlayersInternal();
                Logger.Instance.Info($"Đã load {Players.Count} players");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải danh sách players: {ex.Message}");
            }
        }

        private void UpdatePlayerCount()
        {
            TotalPlayersText.Text = Players.Count.ToString();
        }

        private void OnSearchClick(object? sender, RoutedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.Trim().ToLower();
            if (string.IsNullOrEmpty(searchText))
            {
                PlayersDataGrid.ItemsSource = Players;
                return;
            }

            var filteredPlayers = Players.Where(p =>
                p.CharacterName.ToLower().Contains(searchText)
            ).ToList();

            PlayersDataGrid.ItemsSource = filteredPlayers;
        }

        private void OnRefreshClick(object? sender, RoutedEventArgs e)
        {
            LoadPlayers();
            Logger.Instance.Info("Đã làm mới danh sách players online");
        }

        private void OnPlayerSelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            _selectedPlayer = PlayersDataGrid.SelectedItem as Players;
            var hasSelection = _selectedPlayer != null;

            KickPlayerButton.IsEnabled = hasSelection;
            SendMessageButton.IsEnabled = hasSelection;
            TeleportToPlayerButton.IsEnabled = hasSelection;
            ViewPlayerDetailsButton.IsEnabled = hasSelection;
        }

        private void OnKickPlayerClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Đã kick player: {_selectedPlayer.CharacterName}");
                // TODO: Implement kick logic
            }
        }

        private void OnSendMessageClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Gửi tin nhắn đến player: {_selectedPlayer.CharacterName}");
                // TODO: Implement send message dialog
            }
        }

        private void OnTeleportToPlayerClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Teleport đến player: {_selectedPlayer.CharacterName}");
                // TODO: Implement teleport logic
            }
        }

        private void OnViewPlayerDetailsClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Xem chi tiết player: {_selectedPlayer.CharacterName}");
                // TODO: Implement player details dialog
            }
        }

        private void OnExportPlayersClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Xuất danh sách players");
            // TODO: Implement export functionality
        }

        private void OnBroadcastMessageClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Gửi thông báo toàn server");
            // TODO: Implement broadcast message dialog
        }

        /// <summary>
        /// Toggle auto-refresh on/off
        /// </summary>
        public void ToggleAutoRefresh()
        {
            _autoRefreshEnabled = !_autoRefreshEnabled;
            if (_refreshTimer != null)
            {
                _refreshTimer.Enabled = _autoRefreshEnabled;
            }
            Logger.Instance.Info($"Auto-refresh: {(_autoRefreshEnabled ? "Enabled" : "Disabled")}");
        }

        /// <summary>
        /// Cleanup resources
        /// </summary>
        protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);

            if (_refreshTimer != null)
            {
                _refreshTimer.Stop();
                _refreshTimer.Dispose();
                _refreshTimer = null;
            }
        }
    }

}
