<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="600"
             x:Class="HeroYulgang.Views.DatabaseQueueView"
             x:CompileBindings="False">

    <Grid RowDefinitions="Auto,*">
        <!-- Header Controls -->
        <Grid Grid.Row="0" ColumnDefinitions="*,Auto,Auto,Auto" Margin="10">
            <TextBlock Grid.Column="0" Text="Database Queue Status" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
            
            <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                <CheckBox x:Name="ShowDetailsCheckBox" Content="Hiển thị chi tiết" IsChecked="False"/>
                <CheckBox x:Name="EnableMonitoringCheckBox" Content="Bật monitoring" IsChecked="True"/>
            </StackPanel>
            
            <Button Grid.Column="2" x:Name="RefreshButton" Content="Refresh" Width="100" Height="35" Classes="primary" Margin="5,0"/>
            <Button Grid.Column="3" x:Name="ClearQueueButton" Content="Clear Queue" Width="100" Height="35" Classes="danger" Margin="5,0"/>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Margin="10">
            <StackPanel Spacing="16">
                
                <!-- EF Core Queue Status -->
                <Border Classes="panel" Background="Black">
                    <StackPanel Spacing="12">
                        <TextBlock Text="EF Core Database Queue" Classes="subheader"/>
                        
                        <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0">
                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Spacing="5">
                                <TextBlock Text="Pending Queries" FontWeight="Bold"/>
                                <TextBlock x:Name="EfCorePendingText" Text="0" FontSize="24" Foreground="Orange"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Spacing="5">
                                <TextBlock Text="Executing Queries" FontWeight="Bold"/>
                                <TextBlock x:Name="EfCoreExecutingText" Text="0" FontSize="24" Foreground="Yellow"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Spacing="5">
                                <TextBlock Text="Completed Queries" FontWeight="Bold"/>
                                <TextBlock x:Name="EfCoreCompletedText" Text="0" FontSize="24" Foreground="Green"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="3" Spacing="5">
                                <TextBlock Text="Failed Queries" FontWeight="Bold"/>
                                <TextBlock x:Name="EfCoreFailedText" Text="0" FontSize="24" Foreground="Red"/>
                            </StackPanel>
                            
                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Spacing="5" Margin="0,10,0,0">
                                <TextBlock Text="Average Execution Time" FontWeight="Bold"/>
                                <TextBlock x:Name="EfCoreAvgTimeText" Text="0 ms" FontSize="16" Foreground="Cyan"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Spacing="5" Margin="0,10,0,0">
                                <TextBlock Text="Queue Health" FontWeight="Bold"/>
                                <TextBlock x:Name="EfCoreHealthText" Text="Good" FontSize="16" Foreground="Green"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Connection Pool Queue Status -->
                <Border Classes="panel" Background="Black">
                    <StackPanel Spacing="12">
                        <TextBlock Text="Offline Player Connection Pool" Classes="subheader"/>
                        
                        <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0">
                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Spacing="5">
                                <TextBlock Text="Available Connections" FontWeight="Bold"/>
                                <TextBlock x:Name="PoolAvailableText" Text="0" FontSize="24" Foreground="Green"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="1" Spacing="5">
                                <TextBlock Text="Total Connections" FontWeight="Bold"/>
                                <TextBlock x:Name="PoolTotalText" Text="0" FontSize="24" Foreground="Blue"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2" Spacing="5">
                                <TextBlock Text="Utilization Rate" FontWeight="Bold"/>
                                <TextBlock x:Name="PoolUtilizationText" Text="0%" FontSize="24" Foreground="Orange"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="3" Spacing="5">
                                <TextBlock Text="Queue Length" FontWeight="Bold"/>
                                <TextBlock x:Name="PoolQueueText" Text="0" FontSize="24" Foreground="Yellow"/>
                            </StackPanel>
                            
                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Spacing="5" Margin="0,10,0,0">
                                <TextBlock Text="Pool Status" FontWeight="Bold"/>
                                <TextBlock x:Name="PoolStatusText" Text="Healthy" FontSize="16" Foreground="Green"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Spacing="5" Margin="0,10,0,0">
                                <TextBlock Text="Last Update" FontWeight="Bold"/>
                                <TextBlock x:Name="PoolLastUpdateText" Text="Never" FontSize="16" Foreground="Gray"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Detailed Queue Information (Collapsible) -->
                <Border x:Name="DetailsPanel" Classes="panel" Background="Black" IsVisible="False">
                    <StackPanel Spacing="12">
                        <TextBlock Text="Chi tiết Queue" Classes="subheader"/>
                        
                        <ScrollViewer Height="300">
                            <TextBlock x:Name="DetailsText" Text="Không có thông tin chi tiết" 
                                     FontFamily="Consolas" FontSize="12" 
                                     TextWrapping="Wrap" Foreground="LightGray"/>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Queue Actions -->
                <Border Classes="panel" Background="Black">
                    <StackPanel Spacing="12">
                        <TextBlock Text="Queue Actions" Classes="subheader"/>
                        
                        <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0">
                            <!-- Row 1 -->
                            <Button Grid.Row="0" Grid.Column="0" x:Name="FlushEfCoreButton" 
                                    Content="Flush EF Core Queue" Width="150" Height="40" Classes="warning" Margin="5"/>
                            <Button Grid.Row="0" Grid.Column="1" x:Name="ResetPoolButton" 
                                    Content="Reset Connection Pool" Width="150" Height="40" Classes="danger" Margin="5"/>
                            <Button Grid.Row="0" Grid.Column="2" x:Name="OptimizeQueueButton" 
                                    Content="Optimize Queues" Width="150" Height="40" Classes="success" Margin="5"/>
                            
                            <!-- Row 2 -->
                            <Button Grid.Row="1" Grid.Column="0" x:Name="ExportStatsButton" 
                                    Content="Export Statistics" Width="150" Height="40" Classes="primary" Margin="5"/>
                            <Button Grid.Row="1" Grid.Column="1" x:Name="AnalyzePerformanceButton" 
                                    Content="Analyze Performance" Width="150" Height="40" Classes="primary" Margin="5"/>
                            <Button Grid.Row="1" Grid.Column="2" x:Name="ConfigureAlertsButton" 
                                    Content="Configure Alerts" Width="150" Height="40" Classes="primary" Margin="5"/>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
