using System;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views
{
    public partial class MainView : UserControl
    {
        private readonly World _world;

        public MainView()
        {
            InitializeComponent();
            _world = World.Instance;
            SetupEventHandlers();
            UpdateButtonStates();
        }

        private void SetupEventHandlers()
        {
            StartServerButton.Click += OnStartServerClick;
            StopServerButton.Click += OnStopServerClick;
            RestartServerButton.Click += OnRestartServerClick;
        }

        private async void OnStartServerClick(object? sender, RoutedEventArgs e)
        {
            // Vô hiệu hóa các nút trong khi khởi động
            SetButtonsEnabled(false);

            Logger.Instance.Info("Đang khởi động máy chủ...");

            bool success = await _world.StartAsync();

            if (success)
            {
                Logger.Instance.Info("Máy chủ đã khởi động thành công");
                Logger.Instance.Debug("Đang lắng nghe kết nối từ người chơi");
                Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");
            }
            else
            {
                Logger.Instance.Error("Không thể khởi động máy chủ");
            }

            // Cập nhật trạng thái nút
            UpdateButtonStates();
        }

        private async void OnStopServerClick(object? sender, RoutedEventArgs e)
        {
            // Vô hiệu hóa các nút trong khi dừng
            SetButtonsEnabled(false);

            Logger.Instance.Info("Đang dừng máy chủ...");

            bool success = await _world.StopAsync();

            if (success)
            {
                Logger.Instance.Info("Máy chủ đã dừng thành công");
                Logger.Instance.Debug("Đã đóng tất cả kết nối");
            }
            else
            {
                Logger.Instance.Error("Không thể dừng máy chủ");
            }

            // Cập nhật trạng thái nút
            UpdateButtonStates();
        }

        private async void OnRestartServerClick(object? sender, RoutedEventArgs e)
        {
            // Vô hiệu hóa các nút trong khi khởi động lại
            SetButtonsEnabled(false);

            Logger.Instance.Info("Đang khởi động lại máy chủ...");

            await _world.RestartAsync();

            Logger.Instance.Info("Máy chủ đã khởi động lại thành công");
            Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");

            // Cập nhật trạng thái nút
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            switch (_world.State)
            {
                case WorldState.Stopped:
                    StartServerButton.IsEnabled = true;
                    StopServerButton.IsEnabled = false;
                    RestartServerButton.IsEnabled = false;
                    break;

                case WorldState.Running:
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = true;
                    RestartServerButton.IsEnabled = true;
                    break;

                case WorldState.Starting:
                case WorldState.Stopping:
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = false;
                    RestartServerButton.IsEnabled = false;
                    break;
            }
        }

        private void SetButtonsEnabled(bool enabled)
        {
            StartServerButton.IsEnabled = enabled;
            StopServerButton.IsEnabled = enabled;
            RestartServerButton.IsEnabled = enabled;
        }
    }
}
