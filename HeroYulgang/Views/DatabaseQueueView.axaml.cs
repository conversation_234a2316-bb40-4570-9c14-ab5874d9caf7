using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Timers;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Threading;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views
{
    public partial class DatabaseQueueView : UserControl
    {
        private System.Timers.Timer _monitoringTimer;
        private readonly object _monitoringLock = new object();
        private bool _monitoringEnabled = true;
        private int _monitoringInterval = 1000; // 2 seconds

        // EF Core Queue Stats
        private int _efCorePending = 0;
        private int _efCoreExecuting = 0;
        private int _efCoreCompleted = 0;
        private int _efCoreFailed = 0;
        private double _efCoreAvgTime = 0;

        // Connection Pool Stats
        private int _poolAvailable = 0;
        private int _poolTotal = 0;
        private int _poolQueue = 0;
        private DateTime _lastUpdate = DateTime.Now;

        public DatabaseQueueView()
        {
            InitializeComponent();
            SetupEventHandlers();
            SetupMonitoring();
            LoadInitialData();
        }

        private void SetupEventHandlers()
        {
            RefreshButton.Click += OnRefreshClick;
            ClearQueueButton.Click += OnClearQueueClick;
            ShowDetailsCheckBox.Checked += OnShowDetailsChanged;
            ShowDetailsCheckBox.Unchecked += OnShowDetailsChanged;
            EnableMonitoringCheckBox.Checked += OnMonitoringChanged;
            EnableMonitoringCheckBox.Unchecked += OnMonitoringChanged;

            // Queue Actions
            FlushEfCoreButton.Click += OnFlushEfCoreClick;
            ResetPoolButton.Click += OnResetPoolClick;
            OptimizeQueueButton.Click += OnOptimizeQueueClick;
            ExportStatsButton.Click += OnExportStatsClick;
            AnalyzePerformanceButton.Click += OnAnalyzePerformanceClick;
            ConfigureAlertsButton.Click += OnConfigureAlertsClick;
        }

        private void SetupMonitoring()
        {
            _monitoringTimer = new System.Timers.Timer(_monitoringInterval);
            _monitoringTimer.Elapsed += OnMonitoringTick;
            _monitoringTimer.AutoReset = true;
            _monitoringTimer.Enabled = _monitoringEnabled;
        }

        private void OnMonitoringTick(object sender, ElapsedEventArgs e)
        {
            if (!_monitoringEnabled) return;

            lock (_monitoringLock)
            {
                try
                {
                    // Sử dụng Dispatcher để update UI thread
                    Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        UpdateQueueStats();
                    });
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Lỗi monitoring database queue: {ex.Message}");
                }
            }
        }

        private void LoadInitialData()
        {
            try
            {
                UpdateQueueStats();
                Logger.Instance.Info("Đã load database queue status");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi load initial data: {ex.Message}");
            }
        }

        private void UpdateQueueStats()
        {
            try
            {
                // Update EF Core stats (simulated for now)
                UpdateEfCoreStats();

                // Update Connection Pool stats
                UpdateConnectionPoolStats();

                // Update UI
                UpdateUI();

                _lastUpdate = DateTime.Now;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi update queue stats: {ex.Message}");
            }
        }

        private void UpdateEfCoreStats()
        {
            // Get stats from DatabaseQueueMonitor
            var stats = DatabaseQueueMonitor.Instance.EfCoreStats;
            _efCorePending = stats.PendingQueries;
            _efCoreExecuting = stats.ExecutingQueries;
            _efCoreCompleted = stats.CompletedQueries;
            _efCoreFailed = stats.FailedQueries;
            _efCoreAvgTime = stats.AverageExecutionTimeMs;
        }

        private void UpdateConnectionPoolStats()
        {
            try
            {
                // Get stats from DatabaseQueueMonitor
                var stats = DatabaseQueueMonitor.Instance.PoolStats;
                _poolAvailable = stats.AvailableConnections;
                _poolTotal = stats.TotalConnections;
                _poolQueue = stats.QueueLength;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi get connection pool stats: {ex.Message}");
                _poolAvailable = 0;
                _poolTotal = 0;
                _poolQueue = 0;
            }
        }

        private void UpdateUI()
        {
            // Update EF Core UI
            EfCorePendingText.Text = _efCorePending.ToString();
            EfCoreExecutingText.Text = _efCoreExecuting.ToString();
            EfCoreCompletedText.Text = _efCoreCompleted.ToString();
            EfCoreFailedText.Text = _efCoreFailed.ToString();
            EfCoreAvgTimeText.Text = $"{_efCoreAvgTime:F2} ms";

            // EF Core Health
            string efCoreHealth = GetEfCoreHealth();
            EfCoreHealthText.Text = efCoreHealth;
            EfCoreHealthText.Foreground = GetHealthColor(efCoreHealth);

            // Update Connection Pool UI
            PoolAvailableText.Text = _poolAvailable.ToString();
            PoolTotalText.Text = _poolTotal.ToString();
            PoolQueueText.Text = _poolQueue.ToString();

            // Pool Utilization
            double utilization = _poolTotal > 0 ? (double)(_poolTotal - _poolAvailable) / _poolTotal * 100 : 0;
            PoolUtilizationText.Text = $"{utilization:F1}%";
            PoolUtilizationText.Foreground = GetUtilizationColor(utilization);

            // Pool Status
            string poolStatus = GetPoolStatus(utilization);
            PoolStatusText.Text = poolStatus;
            PoolStatusText.Foreground = GetHealthColor(poolStatus);

            // Last Update
            PoolLastUpdateText.Text = _lastUpdate.ToString("HH:mm:ss");

            // Update details if visible
            if (DetailsPanel.IsVisible)
            {
                UpdateDetailsText();
            }
        }

        private string GetEfCoreHealth()
        {
            if (_efCorePending > 100) return "Critical";
            if (_efCorePending > 50) return "Warning";
            if (_efCoreFailed > 10) return "Warning";
            return "Good";
        }

        private string GetPoolStatus(double utilization)
        {
            if (utilization > 90) return "Critical";
            if (utilization > 70) return "Warning";
            if (_poolTotal < 5) return "Low";
            return "Healthy";
        }

        private Avalonia.Media.IBrush GetHealthColor(string health)
        {
            return health switch
            {
                "Good" or "Healthy" => Avalonia.Media.Brushes.Green,
                "Warning" or "Low" => Avalonia.Media.Brushes.Orange,
                "Critical" => Avalonia.Media.Brushes.Red,
                _ => Avalonia.Media.Brushes.Gray
            };
        }

        private Avalonia.Media.IBrush GetUtilizationColor(double utilization)
        {
            if (utilization > 90) return Avalonia.Media.Brushes.Red;
            if (utilization > 70) return Avalonia.Media.Brushes.Orange;
            if (utilization > 50) return Avalonia.Media.Brushes.Yellow;
            return Avalonia.Media.Brushes.Green;
        }

        private void UpdateDetailsText()
        {
            var details = new List<string>
            {
                "=== EF Core Queue Details ===",
                $"Pending Queries: {_efCorePending}",
                $"Executing Queries: {_efCoreExecuting}",
                $"Completed Queries: {_efCoreCompleted}",
                $"Failed Queries: {_efCoreFailed}",
                $"Average Execution Time: {_efCoreAvgTime:F2} ms",
                "",
                "=== Connection Pool Details ===",
                $"Available Connections: {_poolAvailable}",
                $"Total Connections: {_poolTotal}",
                $"Queue Length: {_poolQueue}",
                $"Utilization: {(_poolTotal > 0 ? (double)(_poolTotal - _poolAvailable) / _poolTotal * 100 : 0):F1}%",
                "",
                "=== System Information ===",
                $"Last Update: {_lastUpdate:yyyy-MM-dd HH:mm:ss}",
                $"Monitoring Enabled: {_monitoringEnabled}",
                $"Update Interval: {_monitoringInterval} ms"
            };

            DetailsText.Text = string.Join("\n", details);
        }

        // Event Handlers
        private void OnRefreshClick(object? sender, RoutedEventArgs e)
        {
            UpdateQueueStats();
            Logger.Instance.Info("Đã refresh database queue status");
        }

        private void OnClearQueueClick(object? sender, RoutedEventArgs e)
        {
            // Reset counters
            _efCoreCompleted = 0;
            _efCoreFailed = 0;
            UpdateUI();
            Logger.Instance.Info("Đã clear queue counters");
        }

        private void OnShowDetailsChanged(object? sender, RoutedEventArgs e)
        {
            DetailsPanel.IsVisible = ShowDetailsCheckBox.IsChecked == true;
            if (DetailsPanel.IsVisible)
            {
                UpdateDetailsText();
            }
        }

        private void OnMonitoringChanged(object? sender, RoutedEventArgs e)
        {
            _monitoringEnabled = EnableMonitoringCheckBox.IsChecked == true;
            if (_monitoringTimer != null)
            {
                _monitoringTimer.Enabled = _monitoringEnabled;
            }
            Logger.Instance.Info($"Database queue monitoring: {(_monitoringEnabled ? "Enabled" : "Disabled")}");
        }

        // Queue Action Event Handlers
        private void OnFlushEfCoreClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Flush EF Core queue requested");
            // TODO: Implement EF Core queue flush
        }

        private void OnResetPoolClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                World.OfflinePlayerConnectionPool.Cleanup();
                Logger.Instance.Info("Connection pool đã được reset");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi reset connection pool: {ex.Message}");
            }
        }

        private void OnOptimizeQueueClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Queue optimization requested");
            // TODO: Implement queue optimization
        }

        private void OnExportStatsClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Export statistics requested");
            // TODO: Implement statistics export
        }

        private void OnAnalyzePerformanceClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Instance.Info("Bắt đầu phân tích performance database queue...");

                var report = DatabaseQueueAnalyzer.Instance.GenerateDetailedReport();

                // Hiển thị report trong details panel
                ShowDetailsCheckBox.IsChecked = true;
                DetailsPanel.IsVisible = true;
                DetailsText.Text = report;

                var issues = DatabaseQueueAnalyzer.Instance.DetectedIssues;
                var criticalCount = issues.Count(i => i.Severity == "Critical");
                var highCount = issues.Count(i => i.Severity == "High");

                Logger.Instance.Info($"Phân tích hoàn thành: {criticalCount} vấn đề nghiêm trọng, {highCount} vấn đề mức cao");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi phân tích performance: {ex.Message}");
            }
        }

        private void OnConfigureAlertsClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Configure alerts requested");
            // TODO: Implement alerts configuration
        }

        // Cleanup
        protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);

            if (_monitoringTimer != null)
            {
                _monitoringTimer.Stop();
                _monitoringTimer.Dispose();
                _monitoringTimer = null;
            }
        }
    }
}
