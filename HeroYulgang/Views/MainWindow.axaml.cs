using System;
using Avalonia.Controls;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();

        // Khởi tạo World
        _ = World.Instance;

        // Log application start
        Logger.Instance.Info("Ứng dụng đã khởi động");
        Logger.Instance.Debug("Chế độ Debug đã được bật");

        // Tải cấu hình
        var config = ConfigManager.Instance;
        Logger.Instance.Info($"Đã tải cấu hình máy chủ: {config.ServerSettings.ServerName}");
        Logger.Instance.Debug($"Cổng máy chủ: {config.ServerSettings.GameServerPort}");
        Logger.Instance.Debug($"Số lượng người chơi tối đa: {config.ServerSettings.MaximumOnline}");

        // Khoi tao Database manager
        DatabaseManager.Instance.Initialize();

        Logger.Instance.Info("Giao diện tab đã được khởi tạo thành công");
    }
}
