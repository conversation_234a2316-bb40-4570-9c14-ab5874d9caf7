using System;
using System.Net;
using System.Threading.Tasks;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.HelperTools;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Test class để kiểm tra login flow
    /// </summary>
    public class LoginFlowTest
    {
        /// <summary>
        /// Test tạo player với SessionID đúng
        /// </summary>
        public static void TestPlayerCreationWithSessionID()
        {
            try
            {
                Console.WriteLine("=== Test Player Creation With SessionID ===");
                
                int testSessionId = 12345;
                string testAccountId = "testuser";
                
                // Tạo player
                var player = new Players
                {
                    AccountID = testAccountId,
                    LanIp = testAccountId
                };
                
                Console.WriteLine($"Player tạo ban đầu - SessionID: {player.SessionID}");
                
                // Đặt SessionID sử dụng reflection
                var sessionIdField = typeof(PlayersBes).GetField("_sessionID", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (sessionIdField != null)
                {
                    sessionIdField.SetValue(player, testSessionId);
                    Console.WriteLine($"Đã đặt SessionID {testSessionId} cho player {testAccountId}");
                    Console.WriteLine($"Player sau khi đặt - SessionID: {player.SessionID}");
                }
                else
                {
                    Console.WriteLine("FAILED: Không thể tìm thấy field _sessionID");
                    return;
                }
                
                // Kiểm tra SessionID
                if (player.SessionID == testSessionId)
                {
                    Console.WriteLine("SUCCESS: SessionID đã được đặt đúng!");
                }
                else
                {
                    Console.WriteLine($"FAILED: SessionID không đúng. Expected: {testSessionId}, Actual: {player.SessionID}");
                }
                
                Console.WriteLine("=== Test hoàn thành ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test thất bại: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Test ActorNetState creation
        /// </summary>
        public static void TestActorNetStateCreation()
        {
            try
            {
                Console.WriteLine("=== Test ActorNetState Creation ===");
                
                int testSessionId = 67890;
                var testEndPoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), 8888);
                
                // Tạo ActorNetState (không thể tạo IActorRef thực, chỉ test logic)
                Console.WriteLine($"Test SessionID: {testSessionId}");
                Console.WriteLine($"Test EndPoint: {testEndPoint}");
                
                // Test logic đồng bộ SessionID
                var player = new Players
                {
                    AccountID = "testuser2",
                    LanIp = "testuser2"
                };
                
                // Đặt SessionID cho player
                var sessionIdField = typeof(PlayersBes).GetField("_sessionID", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (sessionIdField != null)
                {
                    sessionIdField.SetValue(player, testSessionId);
                    Console.WriteLine($"Player SessionID: {player.SessionID}");
                    
                    if (player.SessionID == testSessionId)
                    {
                        Console.WriteLine("SUCCESS: SessionID synchronization logic works!");
                    }
                    else
                    {
                        Console.WriteLine("FAILED: SessionID synchronization failed");
                    }
                }
                
                Console.WriteLine("=== Test hoàn thành ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test thất bại: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Test helper methods cho offline player
        /// </summary>
        public static void TestOfflinePlayerHelpers()
        {
            try
            {
                Console.WriteLine("=== Test Offline Player Helpers ===");
                
                // Tạo offline player
                var offlinePlayer = new Players
                {
                    AccountID = "offline_test",
                    LanIp = "offline_test"
                };
                
                // Test IsOfflinePlayer method
                // bool isOffline1 = offlinePlayer.IsOfflinePlayer();
                // Console.WriteLine($"IsOfflinePlayer (no client): {isOffline1}");
                
                // // Test GetTargetPlayer method
                // var targetPlayer1 = offlinePlayer.GetTargetPlayer();
                // Console.WriteLine($"GetTargetPlayer (no client): {targetPlayer1?.AccountID ?? "null"}");
                
                // // Test CanSendPacket method
                // bool canSend1 = offlinePlayer.CanSendPacket();
                // Console.WriteLine($"CanSendPacket (no client): {canSend1}");
                
                Console.WriteLine("SUCCESS: Helper methods work without crashing!");
                Console.WriteLine("=== Test hoàn thành ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test thất bại: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Test debug logging
        /// </summary>
        public static void TestDebugLogging()
        {
            try
            {
                Console.WriteLine("=== Test Debug Logging ===");
                
                // Test các level logging
                Logger.Instance.Debug("Test debug message");
                Logger.Instance.Info("Test info message");
                Logger.Instance.Warning("Test warning message");
                Logger.Instance.Error("Test error message");
                
                // Test LogHelper
                LogHelper.WriteLine(LogLevel.Debug, "Test LogHelper debug");
                LogHelper.WriteLine(LogLevel.Info, "Test LogHelper info");
                LogHelper.WriteLine(LogLevel.Warning, "Test LogHelper warning");
                LogHelper.WriteLine(LogLevel.Error, "Test LogHelper error");
                
                Console.WriteLine("SUCCESS: Logging works!");
                Console.WriteLine("=== Test hoàn thành ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test thất bại: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Chạy tất cả các test
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("Bắt đầu chạy tất cả các test login flow...\n");
            
            TestPlayerCreationWithSessionID();
            Console.WriteLine();
            
            TestActorNetStateCreation();
            Console.WriteLine();
            
            TestOfflinePlayerHelpers();
            Console.WriteLine();
            
            TestDebugLogging();
            Console.WriteLine();
            
            Console.WriteLine("Hoàn thành tất cả các test login flow!");
        }
    }
}
