using System;
using System.Net;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using RxjhServer;
using RxjhServer.HelperTools;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Test class để kiểm tra offline player functionality
    /// </summary>
    public class OfflinePlayerTest
    {
        /// <summary>
        /// Test AutoLearnSkill cho offline player
        /// </summary>
        public static void TestOfflinePlayerAutoLearnSkill()
        {
            try
            {
                Console.WriteLine("=== Test Offline Player AutoLearnSkill ===");
                
                // Tạo một offline player gi<PERSON> lập
                var offlinePlayer = CreateMockOfflinePlayer();
                
                // Test AutoLearnSkill
                Console.WriteLine("Gọi AutoLearnSkill cho offline player...");
                offlinePlayer.AutoLearnSkill();
                Console.WriteLine("AutoLearnSkill hoàn thành không có lỗi!");
                
                // Test LoadAscentionMagic
                Console.WriteLine("Gọi LoadAscentionMagic cho offline player...");
               // offlinePlayer.LoadAscentionMagic();
                Console.WriteLine("LoadAscentionMagic hoàn thành không có lỗi!");
                
                // Test Init_Item_In_Bag
                Console.WriteLine("Gọi Init_Item_In_Bag cho offline player...");
                offlinePlayer.Init_Item_In_Bag();
                Console.WriteLine("Init_Item_In_Bag hoàn thành không có lỗi!");
                
                Console.WriteLine("=== Test thành công! ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test thất bại: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Test AutoLearnSkill cho online player
        /// </summary>
        public static void TestOnlinePlayerAutoLearnSkill()
        {
            try
            {
                Console.WriteLine("=== Test Online Player AutoLearnSkill ===");
                
                // Tạo một online player giả lập
                var onlinePlayer = CreateMockOnlinePlayer();
                
                // Test AutoLearnSkill
                Console.WriteLine("Gọi AutoLearnSkill cho online player...");
                onlinePlayer.AutoLearnSkill();
                Console.WriteLine("AutoLearnSkill hoàn thành không có lỗi!");
                
                Console.WriteLine("=== Test thành công! ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test thất bại: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Tạo một offline player giả lập để test
        /// </summary>
        private static Players CreateMockOfflinePlayer()
        {
            var player = new Players();
            
            // Thiết lập thông tin cơ bản
            player.AccountID = "test_offline";
            player.CharacterName = "TestOfflinePlayer";
            player.Player_Job = 1;
            player.Player_Job_level = 10;
            player.Player_Level = 50;
            player.Player_Zx = 1;
            
            // Tạo OfflineActorNetState
            var fakeEndPoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), 8888);
            var offlineClient = new OfflineActorNetState(9999, fakeEndPoint);
            offlineClient.TreoMay = true;
            
            player.Client = offlineClient;
            
            // Khởi tạo VoCongMoi array
            player.VoCongMoi = new X_Vo_Cong_Loai[10, 50];
            
            // Khởi tạo Item_In_Bag array
            player.Item_In_Bag = new X_Vat_Pham_Loai[96];
            for (int i = 0; i < 96; i++)
            {
                player.Item_In_Bag[i] = new X_Vat_Pham_Loai();
            }
            
            Console.WriteLine($"Tạo offline player: {player.AccountID}-{player.CharacterName}");
            return player;
        }
        
        /// <summary>
        /// Tạo một online player giả lập để test
        /// </summary>
        private static Players CreateMockOnlinePlayer()
        {
            var player = new Players();
            
            // Thiết lập thông tin cơ bản
            player.AccountID = "test_online";
            player.CharacterName = "TestOnlinePlayer";
            player.Player_Job = 1;
            player.Player_Job_level = 10;
            player.Player_Level = 50;
            player.Player_Zx = 1;
            
            // Tạo ActorNetState thông thường
            var fakeEndPoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), 8888);
            var onlineClient = new ActorNetState(Akka.Actor.ActorRefs.NoSender, 8888, fakeEndPoint);
            onlineClient.Player = player;
            
            player.Client = onlineClient;
            
            // Khởi tạo VoCongMoi array
            player.VoCongMoi = new X_Vo_Cong_Loai[10, 50];
            
            // Khởi tạo Item_In_Bag array
            player.Item_In_Bag = new X_Vat_Pham_Loai[96];
            for (int i = 0; i < 96; i++)
            {
                player.Item_In_Bag[i] = new X_Vat_Pham_Loai();
            }
            
            Console.WriteLine($"Tạo online player: {player.AccountID}-{player.CharacterName}");
            return player;
        }
        
        /// <summary>
        /// Chạy tất cả các test
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("Bắt đầu chạy tất cả các test...\n");
            
            TestOfflinePlayerAutoLearnSkill();
            Console.WriteLine();
            
            TestOnlinePlayerAutoLearnSkill();
            Console.WriteLine();
            
            Console.WriteLine("Hoàn thành tất cả các test!");
        }
    }
}
