using System;
using System.Threading.Tasks;
using System.Threading;
using HeroYulgang.Helpers;
using RxjhServer;
using HeroYulgang.Services;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Test class để kiểm tra việc sửa lỗi DbContext threading
    /// </summary>
    public static class DbContextThreadingTest
    {
        /// <summary>
        /// Test việc đăng nhập nhiều player đồng thời
        /// </summary>
        public static async Task TestConcurrentLogin()
        {
            Logger.Instance.Info("Bắt đầu test đăng nhập đồng thời...");
            
            var tasks = new Task[10];
            
            for (int i = 0; i < 10; i++)
            {
                var playerId = $"testuser{i:D3}";
                tasks[i] = Task.Run(() => SimulatePlayerLogin(playerId));
            }
            
            try
            {
                await Task.WhenAll(tasks);
                Logger.Instance.Info("✓ Test đăng nhập đồng thời thành công!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test đăng nhập đồng thời thất bại: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Giả lập việc đăng nhập của một player
        /// </summary>
        private static void SimulatePlayerLogin(string playerId)
        {
            try
            {
                Logger.Instance.Debug($"Đang giả lập đăng nhập cho {playerId}...");
                
                var player = new Players
                {
                    AccountID = playerId,
                    LanIp = playerId
                };
                
                // Giả lập việc gọi KetNoi_DangNhap2 với dữ liệu test
                // Lưu ý: Trong test thực tế, bạn cần có dữ liệu test trong database
                // player.KetNoi_DangNhap2(playerId, "test", "127.0.0.1", "0", "", "", "", "");
                
                Logger.Instance.Debug($"✓ Đăng nhập thành công cho {playerId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Lỗi khi đăng nhập {playerId}: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Test việc load character offline đồng thời
        /// </summary>
        public static async Task TestConcurrentOfflineLoad()
        {
            Logger.Instance.Info("Bắt đầu test load character offline đồng thời...");
            
            try
            {
                // Gọi method LoadCharacterOffAttack trong một task riêng
                await Task.Run(() => World.LoadCharacterOffAttack());
                Logger.Instance.Info("✓ Test load character offline thành công!");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test load character offline thất bại: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Chạy tất cả các test
        /// </summary>
        public static async Task RunAllTests()
        {
            Logger.Instance.Info("=== Bắt đầu DbContext Threading Tests ===");
            
            await TestConcurrentLogin();
            await Task.Delay(2000); // Delay giữa các test
            
            await TestConcurrentOfflineLoad();
            
            Logger.Instance.Info("=== Hoàn thành DbContext Threading Tests ===");
        }
    }
}
