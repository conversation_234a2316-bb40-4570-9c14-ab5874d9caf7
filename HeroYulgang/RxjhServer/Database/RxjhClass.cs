using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Text;
using System.Security.Cryptography;
using System.Collections.Generic;
using HeroYulgang.Database.Entities.Account;
using HeroYulgang.Database.Entities.Game;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using HeroYulgang.Core;
using RxjhServer.HelperTools;
using HeroYulgang.Helpers;
using HeroYulgang.Database.Entities.Public;
using HeroYulgang.Database.Entities.BBG;
using System.Linq;

namespace RxjhServer.Database
{
    /// <summary>
    /// Lớp tương thích với RxjhClass cũ, sử dụng Entity Framework Core
    /// </summary>
    public static class RxjhClass
    {
        private static readonly DatabaseService _databaseService = new DatabaseService();



        /// <summary>
        /// Lấy thông tin tài khoản
        /// </summary>
        public static async Task<TblAccount> GetAccountAsync(string username)
        {
            return await _databaseService.AccountRepository.GetAccountAsync(username);
        }

        /// <summary>
        /// Xác thực tài khoản
        /// </summary>
        public static async Task<bool> ValidateAccountAsync(string username, string password)
        {
            return await _databaseService.AccountRepository.ValidateAccountAsync(username, password);
        }


        /// <summary>
        /// Lấy thông tin nhân vật theo tên
        /// </summary>
        // public static async Task<DataTable> GetCharacterByNameAsync(string name)
        // {
        //     var character = await _databaseService.CharacterRepository.GetCharacterByNameAsync(name);
        //     if (character == null)
        //     {
        //         return null;
        //     }
        //
        //     var dataTable = new DataTable();
        //     dataTable.Columns.Add("FLD_ID", typeof(int));
        //     dataTable.Columns.Add("FLD_AccountID", typeof(string));
        //     dataTable.Columns.Add("FLD_Name", typeof(string));
        //     dataTable.Columns.Add("FLD_Level", typeof(int));
        //     dataTable.Columns.Add("FLD_Job", typeof(int));
        //     dataTable.Columns.Add("FLD_Sex", typeof(int));
        //     dataTable.Columns.Add("FLD_MapID", typeof(int));
        //     dataTable.Columns.Add("FLD_X", typeof(float));
        //     dataTable.Columns.Add("FLD_Y", typeof(float));
        //     dataTable.Columns.Add("FLD_FactionID", typeof(int));
        //     dataTable.Columns.Add("FLD_Money", typeof(long));
        //     dataTable.Columns.Add("FLD_EXP", typeof(long));
        //     dataTable.Columns.Add("FLD_HP", typeof(int));
        //     dataTable.Columns.Add("FLD_MP", typeof(int));
        //     dataTable.Columns.Add("FLD_SP", typeof(int));
        //     dataTable.Columns.Add("FLD_DATA", typeof(byte[]));
        //
        //     var row = dataTable.NewRow();
        //     row["FLD_ID"] = character.CharId;
        //     row["FLD_AccountID"] = character.AccountId;
        //     row["FLD_Name"] = character.Name;
        //     row["FLD_Level"] = character.Level;
        //     row["FLD_Job"] = character.Job;
        //     row["FLD_Sex"] = character.Sex;
        //     row["FLD_MapID"] = character.MapId;
        //     row["FLD_X"] = character.PosX;
        //     row["FLD_Y"] = character.PosY;
        //     row["FLD_FactionID"] = character.GuildId;
        //     row["FLD_Money"] = character.Gold;
        //     row["FLD_EXP"] = character.Exp;
        //     row["FLD_HP"] = character.Hp;
        //     row["FLD_MP"] = character.Mp;
        //     row["FLD_SP"] = character.Sp;
        //     row["FLD_DATA"] = character.Data;
        //     dataTable.Rows.Add(row);
        //
        //     return dataTable;
        // }

        /// <summary>
        /// Kiểm tra tên nhân vật đã tồn tại chưa
        /// </summary>
        public static async Task<bool> IsCharacterNameExistsAsync(string name)
        {
            return await _databaseService.CharacterRepository.IsNameExistsAsync(name);
        }

        /// <summary>
        /// Tạo nhân vật mới
        /// </summary>
        public static async Task<bool> CreateCharacterAsync(TblXwwlChar character)
        {
            return await _databaseService.CharacterRepository.CreateCharacterAsync(character);
        }

        /// <summary>
        /// Cập nhật nhân vật
        /// </summary>
        public static async Task<bool> UpdateCharacterAsync(TblXwwlChar character)
        {
            return await _databaseService.CharacterRepository.UpdateCharacterAsync(character);
        }

        /// <summary>
        /// Xóa nhân vật
        /// </summary>
        public static async Task<bool> DeleteCharacterAsync(int charId)
        {
            return await _databaseService.CharacterRepository.DeleteCharacterAsync(charId);
        }

        #region Guild Methods

        /// <summary>
        /// Lấy thông tin bang phái
        /// </summary>
        public static async Task<TblXwwlGuild> GetGuildAsync(int guildId)
        {
            return await _databaseService.GuildRepository.GetGuildAsync(guildId);
        }

        /// <summary>
        /// Lấy thông tin bang phái theo tên
        /// </summary>
        public static async Task<TblXwwlGuild> GetGuildByNameAsync(string name)
        {
            return await _databaseService.GuildRepository.GetGuildByNameAsync(name);
        }

        /// <summary>
        /// Tạo bang phái mới
        /// </summary>
        public static async Task<bool> CreateGuildAsync(TblXwwlGuild guild)
        {
            return await _databaseService.GuildRepository.CreateGuildAsync(guild);
        }

        /// <summary>
        /// Cập nhật bang phái
        /// </summary>
        public static async Task<bool> UpdateGuildAsync(TblXwwlGuild guild)
        {
            return await _databaseService.GuildRepository.UpdateGuildAsync(guild);
        }

        /// <summary>
        /// Xóa bang phái
        /// </summary>
        public static async Task<bool> DeleteGuildAsync(int guildId)
        {
            return await _databaseService.GuildRepository.DeleteGuildAsync(guildId);
        }


        /// <summary>
        /// Thêm thành viên vào bang phái
        /// </summary>
        public static async Task<bool> AddGuildMemberAsync(TblXwwlGuildMember member)
        {
            return await _databaseService.GuildRepository.AddGuildMemberAsync(member);
        }

        /// <summary>
        /// Xóa thành viên khỏi bang phái
        /// </summary>
        public static async Task<bool> RemoveGuildMemberAsync(int guildId, int charId)
        {
            return await _databaseService.GuildRepository.RemoveGuildMemberAsync(guildId, charId);
        }

        #endregion

        #region Bang Phai Vinh Du Methods

        /// <summary>
        /// Cập nhật điểm vinh dự cho bang phái
        /// </summary>
        public static async Task<int> SetBangPhaiVinhDuSoLieu(string bangPhaiName, string bangPhaiMonChu, int theLuc, int dangCap, int ngheNghiep, int diemSo)
        {
            try
            {
                // Kiểm tra xem bang phái đã có trong bảng vinh dự chưa
                var query = $"SELECT * FROM TBL_VinhDuHeThong WHERE FLD_TYPE = 3 AND FLD_BangPhai = @mpname";
                var parameters = new SqlParameter[] { SqlDBA.MakeInParam("@mpname", SqlDbType.VarChar, 30, bangPhaiName) };
                var dataTable = DBA.GetDBToDataTable(query, parameters);

                if (dataTable == null)
                {
                    return -1;
                }

                // Nếu chưa có, tạo mới bản ghi
                if (dataTable.Rows.Count == 0)
                {
                    dataTable.Dispose();
                    var insertQuery = $"INSERT INTO TBL_VinhDuHeThong (FLD_TYPE, FLD_NgheNghiep, FLD_TenNhanVat, FLD_DangCap, FLD_TheLuc, FLD_BangPhai, FLD_BangPhaiMonChu, FLD_DiemSo) " +
                                     $"VALUES (3, {ngheNghiep}, '', {dangCap}, {theLuc}, '{bangPhaiName}', '{bangPhaiMonChu}', {diemSo})";

                    var res = await DBA.ExeSqlCommand(insertQuery, "GameServer");
                    if (res != -1)
                    {
                        return 1;
                    }
                    return -1;
                }

                // Nếu đã có, cập nhật điểm vinh dự
                dataTable.Dispose();
                var updateQuery = $"UPDATE TBL_VinhDuHeThong SET FLD_DiemSo = FLD_DiemSo + {diemSo} WHERE FLD_BangPhai = '{bangPhaiName}' AND FLD_TYPE = 3";

                if (await DBA.ExeSqlCommand(updateQuery, "GameServer") != -1)
                {
                    return 1;
                }
                return -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong SetBangPhaiVinhDuSoLieu: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Cập nhật điểm vinh dự cho người chơi
        /// </summary>
        public static int Set_NguoiVinhDu_SoLieu(int loaiVinh, string tenNhanVat, int dangCap, int ngheNghiep, int theLuc, string bangPhai, string bangPhaiMonChu, int diemSo)
        {
            try
            {
                // Lấy thông tin bang phái để cập nhật tên bang chủ
                var dataTable = DatDuocBangPhaiSoLieu(bangPhai);
                if (dataTable != null)
                {
                    if (dataTable.Rows.Count > 0)
                    {
                        bangPhaiMonChu = dataTable.Rows[0]["G_Master"].ToString();
                    }
                    dataTable.Dispose();
                }

                // Kiểm tra xem người chơi đã có trong bảng vinh dự chưa
                var query = $"SELECT FLD_TenNhanVat FROM TBL_VinhDuHeThong WHERE FLD_TenNhanVat = @name AND FLD_TYPE = @lx";
                var parameters = new SqlParameter[]
                {
                    SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, tenNhanVat),
                    SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, loaiVinh)
                };
                var dBToDataTable = DBA.GetDBToDataTable(query, parameters);

                if (dBToDataTable == null)
                {
                    return -1;
                }

                // Nếu chưa có, tạo mới bản ghi
                if (dBToDataTable.Rows.Count == 0)
                {
                    dBToDataTable.Dispose();
                    var insertQuery = $"INSERT INTO TBL_VinhDuHeThong (FLD_TYPE, FLD_NgheNghiep, FLD_TenNhanVat, FLD_DangCap, FLD_TheLuc, FLD_BangPhai, FLD_BangPhaiMonChu, FLD_DiemSo) " +
                                     $"VALUES ({loaiVinh}, {ngheNghiep}, '{tenNhanVat}', {dangCap}, {theLuc}, '{bangPhai}', '{bangPhaiMonChu}', {diemSo})";

                    if (DBA.ExeSqlCommand(insertQuery, "GameServer").GetAwaiter().GetResult() != -1)
                    {
                        return 1;
                    }
                    return -1;
                }

                // Nếu đã có, cập nhật điểm vinh dự
                dBToDataTable.Dispose();
                var updateQuery = $"UPDATE TBL_VinhDuHeThong SET FLD_DiemSo = FLD_DiemSo + {diemSo} WHERE FLD_TenNhanVat = '{tenNhanVat}' AND FLD_TYPE = {loaiVinh}";

                if (DBA.ExeSqlCommand(updateQuery, "GameServer").GetAwaiter().GetResult() != -1)
                {
                    return 1;
                }
                return -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong Set_NguoiVinhDu_SoLieu: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Lấy thông tin bang phái
        /// </summary>
        public static DataTable DatDuocBangPhaiSoLieu(string bangPhaiName)
        {
            try
            {
                var query = "SELECT * FROM TBL_XWWL_Guild WHERE G_Name = @name";
                var parameters = new SqlParameter[] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, bangPhaiName) };
                var dataTable = DBA.GetDBToDataTable(query, parameters);

                if (dataTable == null)
                {
                    return null;
                }

                if (dataTable.Rows.Count != 0)
                {
                    return dataTable;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong DatDuocBangPhaiSoLieu: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Lấy thông tin vinh dự của bang phái
        /// </summary>
        public static DataTable DatDuocBangPhaiVinhDuSoLieu(string bangPhaiName, string faction)
        {
            try
            {
                var query = "SELECT * FROM VinhDuBangPhaiXepHang WHERE FLD_BP = @name AND FLD_FQ = @fq";
                var parameters = new SqlParameter[]
                {
                    SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, bangPhaiName),
                    SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, faction)
                };
                var dataTable = DBA.GetDBToDataTable(query, parameters);

                if (dataTable == null)
                {
                    return null;
                }

                if (dataTable.Rows.Count != 0)
                {
                    return dataTable;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong DatDuocBangPhaiVinhDuSoLieu: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Tạo mới bản ghi vinh dự cho bang phái
        /// </summary>
        public static void TaoMoi_BangPhaiVinhDu(string tenNhanVat, string bangPhaiName, int theLuc, int dangCap, int ngheNghiep, int jobLevel, int rongYu, string faction)
        {
            try
            {
                var query = "EXEC INT_menpai_DATA_New @rwname, @bpname, @zx, @leve, @job, @jobleve, @rongyu, @fq";
                var parameters = new SqlParameter[]
                {
                    SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, tenNhanVat),
                    SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, bangPhaiName),
                    SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, theLuc),
                    SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, dangCap),
                    SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, ngheNghiep),
                    SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, jobLevel),
                    SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, rongYu),
                    SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, faction)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong TaoMoi_BangPhaiVinhDu: {ex.Message}");
            }
        }


        #endregion

        /// <summary>
        /// Đặt trạng thái người dùng thành offline
        /// </summary>
        /// <param name="id">ID của tài khoản</param>
        public static async void SetUser_Offline(string id)
        {
            try
            {
                // Sử dụng SQL trực tiếp để cập nhật trạng thái người dùng
                var query = "UPDATE TBL_ACCOUNT SET FLD_ONLINE = 0, FLD_CHECKLOGIN = 'false', FLD_CHECKIP = null WHERE FLD_ID = @id";
                var parameters = new SqlParameter[] { new SqlParameter("@id", id) };

                // Sử dụng phương thức DBA hiện có để thực thi truy vấn
                await DBA.ExeSqlCommand(query, parameters, "rxjhaccount");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong SetUser_Offline: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa địa chỉ MAC khi người dùng đăng xuất
        /// </summary>
        /// <param name="username">Tên người dùng</param>
        public static async void Delete_Recovery_MAC_Address_Logout(string username)
        {
            try
            {
                // Sử dụng SQL trực tiếp để xóa bản ghi MAC
                var query = "DELETE FROM LoginRecord_MAC WHERE UserName = @username AND LoaiHinh = 'Login'";
                var parameters = new SqlParameter[] { new SqlParameter("@username", username) };

                // Sử dụng phương thức DBA hiện có để thực thi truy vấn
                await DBA.ExeSqlCommand(query, parameters);
                //DBA.GetDatabaseConnection("game")
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong Delete_Recovery_MAC_Address_Logout: {ex.Message}");
            }
        }

        /// <summary>
        /// Ghi lại thông tin đăng nhập của người dùng
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="userIp">Địa chỉ IP</param>
        /// <param name="loaiHinh">Loại hình đăng nhập</param>
        /// <param name="macAddress">Địa chỉ MAC</param>
        public static async void LoginRecord(string userId, string userName, string userIp, string loaiHinh, string macAddress)
        {
            if (World.LoginRecord != 1)
            {
                return;
            }

            try
            {
                // Sử dụng SQL trực tiếp để thêm bản ghi đăng nhập
                var query = "INSERT INTO LoginRecord (UserId, UserName, UserIp, LoaiHinh, Mac_Address) VALUES (@UserId, @UserName, @UserIp, @LoaiHinh, @Mac_Address)";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@UserName", userName),
                    new SqlParameter("@UserIp", userIp),
                    new SqlParameter("@LoaiHinh", loaiHinh),
                    new SqlParameter("@Mac_Address", macAddress)
                };

                // Sử dụng phương thức DBA hiện có để thực thi truy vấn
                await DBA.ExeSqlCommand(query, parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong LoginRecord: {ex.Message}");
            }
        }

        /// <summary>
        /// Ghi lại thông tin đăng nhập của người dùng kèm địa chỉ MAC và ID máy chủ
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="userIp">Địa chỉ IP</param>
        /// <param name="loaiHinh">Loại hình đăng nhập</param>
        /// <param name="macAddress">Địa chỉ MAC</param>
        /// <param name="serverId">ID máy chủ</param>
        public static async void LoginRecordMac(string userId, string userName, string userIp, string loaiHinh, string macAddress, int serverId)
        {
            if (World.LoginRecord != 1)
            {
                return;
            }

            try
            {
                // Sử dụng SQL trực tiếp để thêm bản ghi đăng nhập
                var query = "INSERT INTO LoginRecord_MAC (UserId, UserName, UserIp, LoaiHinh, Mac_Address, ServerID) VALUES (@UserId, @UserName, @UserIp, @LoaiHinh, @Mac_Address, @ServerID)";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@UserName", userName),
                    new SqlParameter("@UserIp", userIp),
                    new SqlParameter("@LoaiHinh", loaiHinh),
                    new SqlParameter("@Mac_Address", macAddress),
                    new SqlParameter("@ServerID", serverId)
                };

                // Sử dụng phương thức DBA hiện có để thực thi truy vấn
                await DBA.ExeSqlCommand(query, parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong LoginRecordMac: {ex.Message}");
            }
        }

        /// <summary>
        /// Ghi lại thông tin về các vật phẩm
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="toUserId">ID người nhận</param>
        /// <param name="toUserName">Tên người nhận</param>
        /// <param name="globalId">ID toàn cục của vật phẩm</param>
        /// <param name="itemId">ID vật phẩm</param>
        /// <param name="itemName">Tên vật phẩm</param>
        /// <param name="itemCount">Số lượng vật phẩm</param>
        /// <param name="itemProperties">Thuộc tính vật phẩm</param>
        /// <param name="money">Số tiền</param>
        /// <param name="type">Loại hình</param>
        public static async void ItemRecord(string userId, string userName, string toUserId, string toUserName, double globalId, int itemId, string itemName, int itemCount, string itemProperties, int money, string type)
        {
            if (World.ItemRecord != 1)
            {
                return;
            }

            try
            {
                // Sử dụng SQL trực tiếp để thêm bản ghi vật phẩm
                var query = "INSERT INTO ItemRecord (UserId, UserName, ToUserId, ToUserName, Global_ID, VatPham_ID, VatPhamTen, VatPhamSoLuong, VatPhamThuocTinh, SoTien, LoaiHinh) " +
                           "VALUES (@UserId, @UserName, @ToUserId, @ToUserName, @Global_ID, @VatPham_ID, @VatPhamTen, @VatPhamSoLuong, @VatPham_ThuocTinh, @SoTien, @LoaiHinh)";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@UserName", userName),
                    new SqlParameter("@ToUserId", toUserId),
                    new SqlParameter("@ToUserName", toUserName),
                    new SqlParameter("@Global_ID", globalId.ToString()),
                    new SqlParameter("@VatPham_ID", itemId.ToString()),
                    new SqlParameter("@VatPhamTen", itemName),
                    new SqlParameter("@VatPhamSoLuong", itemCount),
                    new SqlParameter("@VatPham_ThuocTinh", itemProperties),
                    new SqlParameter("@SoTien", money),
                    new SqlParameter("@LoaiHinh", type)
                };

                // Sử dụng phương thức DBA hiện có để thực thi truy vấn
                await DBA.ExeSqlCommand(query, parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong ItemRecord: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra xem tên người dùng có tồn tại không
        /// </summary>
        /// <param name="name">Tên người dùng cần kiểm tra</param>
        /// <returns>-1 nếu có lỗi hoặc tên đã tồn tại, 1 nếu tên chưa tồn tại</returns>
        public static  int GetUserName(string name)
        {
            try
            {
                // Sử dụng Entity Framework Core để kiểm tra tên người dùng
                var exists = _databaseService.CharacterRepository.IsNameExistsAsync(name).GetAwaiter().GetResult();

                // Trả về -1 nếu tên đã tồn tại, 1 nếu tên chưa tồn tại
                return exists ? -1 : 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong GetUserName: {ex.Message}");

                // Fallback sử dụng SQL trực tiếp nếu có lỗi
                var query = "SELECT FLD_NAME FROM TBL_XWWL_Char WHERE FLD_NAME = @name";
                var parameters = new SqlParameter[] { new SqlParameter("@name", name) };

                var dataTable = DBA.GetDBToDataTable(query, parameters);
                if (dataTable == null)
                {
                    return -1;
                }

                if (dataTable.Rows.Count == 0)
                {
                    dataTable.Dispose();
                    return 1;
                }

                dataTable.Dispose();
                return -1;
            }
        }

        /// <summary>
        /// Thay đổi trạng thái hôn nhân của nhân vật
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        /// <param name="status">Trạng thái hôn nhân mới</param>
        public static void ThayDoiHonNhan_TrangThai(string characterName, int status)
        {
            try
            {
                // Sử dụng Entity Framework Core để cập nhật trạng thái hôn nhân
                var character = _databaseService.CharacterRepository.GetCharacterByNameAsync(characterName).GetAwaiter().GetResult();
                if (character != null)
                {
                    character.FldMaritalStatus = status;
                    _databaseService.CharacterRepository.UpdateCharacterAsync(character).GetAwaiter().GetResult();
                }
                else
                {
                    // Fallback sử dụng SQL trực tiếp nếu không tìm thấy nhân vật
                    var query = "UPDATE TBL_XWWL_Char SET FLD_MARITAL_STATUS = @status WHERE FLD_NAME = @name";
                    var parameters = new SqlParameter[]
                    {
                        new SqlParameter("@status", status),
                        new SqlParameter("@name", characterName)
                    };

                    DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong ThayDoiHonNhan_TrangThai: {ex.Message}");

                // Fallback sử dụng SQL trực tiếp nếu có lỗi
                var query = "UPDATE TBL_XWWL_Char SET FLD_MARITAL_STATUS = @status WHERE FLD_NAME = @name";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@status", status),
                    new SqlParameter("@name", characterName)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
        }

        /// <summary>
        /// Kiểm tra và tạo mới thông tin Cw cho người dùng
        /// </summary>
        /// <param name="cwName">Tên Cw</param>
        /// <param name="ownerName">Tên chủ sở hữu</param>
        /// <param name="type">Loại Cw</param>
        /// <param name="id">ID Cw</param>
        /// <returns>1 nếu thành công, -1 nếu thất bại</returns>
        public static int GetCwUserName(string cwName, string ownerName, int type, long id)
        {
            try
            {
                // Kiểm tra xem Cw đã tồn tại chưa
                var query = "SELECT Name FROM TBL_XWWL_Cw WHERE Name = @name";
                var parameters = new SqlParameter[] { new SqlParameter("@name", cwName) };

                var dataTable = DBA.GetDBToDataTable(query, parameters);
                if (dataTable == null)
                {
                    return -1;
                }

                if (dataTable.Rows.Count == 0)
                {
                    dataTable.Dispose();

                    // Tạo mới Cw nếu chưa tồn tại
                    var insertQuery = "EXEC XWWL_INT_Cw_DATA @zrname, @name, @id, @type, @zb1, @zb2";
                    var insertParameters = new SqlParameter[]
                    {
                        new SqlParameter("@zrname", ownerName),
                        new SqlParameter("@name", cwName),
                        new SqlParameter("@id", id),
                        new SqlParameter("@type", type),
                        new SqlParameter("@zb1", new byte[5 * World.Item_Db_Byte_Length]),
                        new SqlParameter("@zb2", new byte[16 * World.Item_Db_Byte_Length])
                    };

                    if (DBA.ExeSqlCommand(insertQuery, insertParameters).GetAwaiter().GetResult() != -1)
                    {
                        return 1;
                    }

                    return -1;
                }

                dataTable.Dispose();
                return -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong GetCwUserName: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Xóa bản ghi tiền đặt cược trong bang chiến
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="guildId">ID bang phái</param>
        /// <param name="result">Kết quả (-1: thua, 0: hòa, 1: thắng)</param>
        public static void BangChien_TienDatCuoc_XoaBo(string userId, string userName, int guildId, int result)
        {
            try
            {
                // Xóa bản ghi tiền đặt cược
                var deleteQuery = "DELETE FROM BangChien_TienDatCuoc WHERE UserId = @userId AND UserName = @userName AND UserName = @guildId";
                var deleteParameters = new SqlParameter[]
                {
                    new SqlParameter("@userId", userId),
                    new SqlParameter("@userName", userName),
                    new SqlParameter("@guildId", guildId)
                };

                DBA.ExeSqlCommand(deleteQuery, deleteParameters).GetAwaiter().GetResult();

                // Cập nhật thống kê bang phái dựa trên kết quả
                switch (result)
                {
                    case -1: // Thua
                        var loseQuery = "UPDATE TBL_XWWL_Guild SET Thua = Thua + 1 WHERE ID = @guildId";
                        var loseParameters = new SqlParameter[] { new SqlParameter("@guildId", guildId) };
                        DBA.ExeSqlCommand(loseQuery, loseParameters).GetAwaiter().GetResult();
                        break;
                    case 0: // Hòa
                        var drawQuery = "UPDATE TBL_XWWL_Guild SET Hoa = Hoa + 1 WHERE ID = @guildId";
                        var drawParameters = new SqlParameter[] { new SqlParameter("@guildId", guildId) };
                        DBA.ExeSqlCommand(drawQuery, drawParameters).GetAwaiter().GetResult();
                        break;
                    case 1: // Thắng
                        var winQuery = "UPDATE TBL_XWWL_Guild SET Thang = Thang + 1 WHERE ID = @guildId";
                        var winParameters = new SqlParameter[] { new SqlParameter("@guildId", guildId) };
                        DBA.ExeSqlCommand(winQuery, winParameters).GetAwaiter().GetResult();
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong BangChien_TienDatCuoc_XoaBo: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm bản ghi tiền đặt cược trong bang chiến
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="guildId">ID bang phái</param>
        /// <param name="amount">Số lượng nguyên bảo</param>
        public static void BangChien_TienDatCuoc(string userId, string userName, int guildId, int amount)
        {
            try
            {
                // Thêm bản ghi tiền đặt cược
                var query = "INSERT INTO BangChien_TienDatCuoc (UserId, UserName, BangPhaiID, NguyenBaoSoLuong) VALUES (@userId, @userName, @guildId, @amount)";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@userId", userId),
                    new SqlParameter("@userName", userName),
                    new SqlParameter("@guildId", guildId),
                    new SqlParameter("@amount", amount)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong BangChien_TienDatCuoc: {ex.Message}");
            }
        }

        /// <summary>
        /// Thay đổi dịch vụ cửa bang phái
        /// </summary>
        /// <param name="guildId">ID bang phái</param>
        /// <param name="word">Từ khóa cửa</param>
        /// <param name="color">Màu sắc cửa</param>
        public static void ChangeDoorService(int guildId, int word, int color)
        {
            try
            {
                // Sử dụng Entity Framework Core để cập nhật dịch vụ cửa
                var guild = _databaseService.GuildRepository.GetGuildAsync(guildId).GetAwaiter().GetResult();
                if (guild != null)
                {
                    guild.MonPhucWord = word;
                    guild.MonPhucMauSac = color;
                    _databaseService.GuildRepository.UpdateGuildAsync(guild).GetAwaiter().GetResult();
                }
                else
                {
                    // Fallback sử dụng SQL trực tiếp nếu không tìm thấy bang phái
                    var query = "UPDATE TBL_XWWL_Guild SET MonPhucWord = @word, MonPhucMauSac = @color WHERE ID = @guildId";
                    var parameters = new SqlParameter[]
                    {
                        new SqlParameter("@word", word),
                        new SqlParameter("@color", color),
                        new SqlParameter("@guildId", guildId)
                    };

                    DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong ChangeDoorService: {ex.Message}");

                // Fallback sử dụng SQL trực tiếp nếu có lỗi
                var query = "UPDATE TBL_XWWL_Guild SET MonPhucWord = @word, MonPhucMauSac = @color WHERE ID = @guildId";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@word", word),
                    new SqlParameter("@color", color),
                    new SqlParameter("@guildId", guildId)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
        }

        /// <summary>
        /// Đăng ký huy hiệu cửa bang phái
        /// </summary>
        /// <param name="guildId">ID bang phái</param>
        /// <param name="badge">Dữ liệu huy hiệu</param>
        public static void ApplyForDoorBadge(int guildId, byte[] badge)
        {
            try
            {
                // Sử dụng Entity Framework Core để cập nhật huy hiệu cửa
                var guild = _databaseService.GuildRepository.GetGuildAsync(guildId).GetAwaiter().GetResult();
                if (guild != null)
                {
                    guild.MonHuy = badge;
                    _databaseService.GuildRepository.UpdateGuildAsync(guild).GetAwaiter().GetResult();
                }
                else
                {
                    // Fallback sử dụng SQL trực tiếp nếu không tìm thấy bang phái
                    var query = "UPDATE TBL_XWWL_Guild SET MonHuy = @badge WHERE ID = @guildId";
                    var parameters = new SqlParameter[]
                    {
                        new SqlParameter("@badge", badge),
                        new SqlParameter("@guildId", guildId)
                    };

                    DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong ApplyForDoorBadge: {ex.Message}");

                // Fallback sử dụng SQL trực tiếp nếu có lỗi
                var query = "UPDATE TBL_XWWL_Guild SET MonHuy = @badge WHERE ID = @guildId";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@badge", badge),
                    new SqlParameter("@guildId", guildId)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
        }

        /// <summary>
        /// Lấy huy hiệu cửa bang phái
        /// </summary>
        /// <param name="guildId">ID bang phái</param>
        /// <returns>Dữ liệu huy hiệu hoặc null nếu không tìm thấy</returns>
        public static byte[] GetTheDoorBadge(int guildId)
        {
            try
            {
                // Sử dụng Entity Framework Core để lấy huy hiệu cửa
                var guild = _databaseService.GuildRepository.GetGuildAsync(guildId).GetAwaiter().GetResult();
                if (guild != null && guild.MonHuy != null)
                {
                    return guild.MonHuy;
                }

                // Fallback sử dụng SQL trực tiếp nếu không tìm thấy bang phái
                var query = "SELECT MonHuy FROM TBL_XWWL_Guild WHERE ID = @guildId";
                var parameters = new SqlParameter[] { new SqlParameter("@guildId", guildId) };

                var dataTable = DBA.GetDBToDataTable(query, parameters);
                if (dataTable == null || dataTable.Rows.Count == 0)
                {
                    return null;
                }

                if (dataTable.Rows[0]["MonHuy"] == DBNull.Value)
                {
                    dataTable.Dispose();
                    return null;
                }

                var badge = (byte[])dataTable.Rows[0]["MonHuy"];
                dataTable.Dispose();
                return badge;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong GetTheDoorBadge: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Chuyển chức vụ bang chủ
        /// </summary>
        /// <param name="newMasterName">Tên bang chủ mới</param>
        /// <param name="oldMasterName">Tên bang chủ cũ</param>
        /// <param name="guildName">Tên bang phái</param>
        public static void ChuyenBangChu_ChucVi(string newMasterName, string oldMasterName, string guildName)
        {
            try
            {
                // Cập nhật chức vụ của bang chủ cũ
                var updateMemberQuery = "UPDATE TBL_XWWL_GuildMember SET Leve = @level WHERE FLD_NAME = @name";
                var updateMemberParameters = new SqlParameter[]
                {
                    new SqlParameter("@level", 5), // Chức vụ mới cho bang chủ cũ
                    new SqlParameter("@name", oldMasterName)
                };

                DBA.ExeSqlCommand(updateMemberQuery, updateMemberParameters).GetAwaiter().GetResult();

                // Cập nhật tên bang chủ mới trong bảng bang phái
                var updateGuildQuery = "UPDATE TBL_XWWL_Guild SET G_Master = @masterName WHERE G_Name = @guildName";
                var updateGuildParameters = new SqlParameter[]
                {
                    new SqlParameter("@masterName", newMasterName),
                    new SqlParameter("@guildName", guildName)
                };

                DBA.ExeSqlCommand(updateGuildQuery, updateGuildParameters).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong ChuyenBangChu_ChucVi: {ex.Message}");
            }
        }

        /// <summary>
        /// Gán vị trí trong bang phái
        /// </summary>
        /// <param name="position">Vị trí (chức vụ)</param>
        /// <param name="memberName">Tên thành viên</param>
        public static void BangPhaiAssignAPosition(int position, string memberName)
        {
            try
            {
                // Cập nhật chức vụ của thành viên
                var query = "UPDATE TBL_XWWL_GuildMember SET Leve = @position WHERE FLD_NAME = @name";
                var parameters = new SqlParameter[]
                {
                    new SqlParameter("@position", position),
                    new SqlParameter("@name", memberName)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong BangPhaiAssignAPosition: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy danh sách thành viên trong bang phái
        /// </summary>
        /// <param name="guildName">Tên bang phái</param>
        /// <returns>DataTable chứa thông tin thành viên hoặc null nếu không tìm thấy</returns>
        public static DataTable DatDuocBangPhai_SoLuongMember(string guildName)
        {
            try
            {
                // Lấy danh sách thành viên trong bang phái
                var query = "SELECT * FROM TBL_XWWL_GuildMember WHERE G_Name = @name";
                var parameters = new SqlParameter[] { new SqlParameter("@name", guildName) };

                var dataTable = DBA.GetDBToDataTable(query, parameters);
                if (dataTable == null || dataTable.Rows.Count == 0)
                {
                    return null;
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong DatDuocBangPhai_SoLuongMember: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Thêm thành viên vào bang phái
        /// </summary>
        /// <param name="memberName">Tên thành viên</param>
        /// <param name="guildName">Tên bang phái</param>
        /// <param name="position">Vị trí (chức vụ)</param>
        /// <returns>Kết quả thực hiện (1: thành công, -1: thất bại)</returns>
        public static int GiaNhapBangPhai(string memberName, string guildName, int position)
        {
            try
            {
                // Sử dụng stored procedure để thêm thành viên vào bang phái
                var query = $"EXEC XWWL_JR_Guild_DATA_New '{memberName}', '{guildName}', {position}";

                var result = DBA.GetDBValue_3(query);
                return result != null ? (int)result : -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong GiaNhapBangPhai: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Rời khỏi bang phái
        /// </summary>
        /// <param name="memberName">Tên thành viên</param>
        /// <returns>Kết quả thực hiện (1: thành công, -1: thất bại)</returns>
        public static int RoiKhoiBangPhai(string memberName)
        {
            try
            {
                // Sử dụng stored procedure để xóa thành viên khỏi bang phái
                var query = $"EXEC XWWL_Out_Guild_DATA '{memberName}'";

                var result = DBA.GetDBValue_3(query);
                return result != null ? (int)result : -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong RoiKhoiBangPhai: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Trục xuất thành viên khỏi bang phái
        /// </summary>
        /// <param name="memberName">Tên thành viên</param>
        /// <param name="guildName">Tên bang phái</param>
        /// <returns>Kết quả thực hiện (1: thành công, -1: thất bại)</returns>
        public static int TrucXuatBangPhai(string memberName, string guildName)
        {
            try
            {
                // Sử dụng stored procedure để trục xuất thành viên khỏi bang phái
                var query = $"EXEC XWWL_OutBz_Guild_DATA '{memberName}', '{guildName}'";

                var result = DBA.GetDBValue_3(query);
                return result != null ? (int)result : -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong TrucXuatBangPhai: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Tạo mới bang phái
        /// </summary>
        /// <param name="masterName">Tên bang chủ</param>
        /// <param name="guildName">Tên bang phái</param>
        /// <param name="position">Vị trí (chức vụ)</param>
        /// <returns>Kết quả thực hiện (1: thành công, -1: thất bại)</returns>
        public static int TaoMoi_BangPhai(string masterName, string guildName, int position)
        {
            try
            {
                // Sử dụng stored procedure để tạo mới bang phái
                var query = $"EXEC XWWL_INT_Guild_DATA_New '{masterName}', '{guildName}', {position}";

                var result = DBA.GetDBValue_3(query);
                return result != null ? (int)result : -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong TaoMoi_BangPhai: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Kiểm tra tên bang phái đã tồn tại chưa
        /// </summary>
        /// <param name="guildName">Tên bang phái</param>
        /// <returns>Kết quả kiểm tra (1: đã tồn tại, -1: chưa tồn tại)</returns>
        public static int TaoMoi_BangPhai_XacNhan(string guildName)
        {
            try
            {
                // Sử dụng stored procedure để kiểm tra tên bang phái
                var query = $"EXEC XWWL_SELECT_Guild_DATA '{guildName}'";

                var result = DBA.GetDBValue_3(query);
                return result != null ? (int)result : -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong TaoMoi_BangPhai_XacNhan: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Lấy dữ liệu liên minh minh chủ
        /// </summary>
        /// <param name="ownerName">Tên người chiếm giữ</param>
        /// <returns>DataTable chứa thông tin liên minh hoặc null nếu không tìm thấy</returns>
        public static DataTable Load_DuLieu_LienMinh_MinhChu(string ownerName)
        {
            try
            {
                if (string.IsNullOrEmpty(ownerName))
                {
                    return null;
                }

                // Lấy dữ liệu liên minh minh chủ
                var query = "SELECT * FROM TBL_XWWL_LienMinhMinhChu WHERE FLD_NAME = @name";
                var parameters = new SqlParameter[] { new SqlParameter("@name", ownerName) };

                var dataTable = DBA.GetDBToDataTable(query, parameters);
                if (dataTable == null || dataTable.Rows.Count == 0)
                {
                    return null;
                }

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong Load_DuLieu_LienMinh_MinhChu: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Đặt trạng thái nhân vật thành offline
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        public static void SetChar_Offline(string characterName)
        {
            try
            {
                // Sử dụng Entity Framework Core để cập nhật trạng thái nhân vật
                var character = _databaseService.CharacterRepository.GetCharacterByNameAsync(characterName).GetAwaiter().GetResult();
                if (character != null)
                {
                    character.FldOnline = 0;
                    _databaseService.CharacterRepository.UpdateCharacterAsync(character).GetAwaiter().GetResult();
                }
                else
                {
                    // Fallback sử dụng SQL trực tiếp nếu không tìm thấy nhân vật
                    var query = "UPDATE TBL_XWWL_Char SET FLD_ONLINE = 0 WHERE FLD_NAME = @name";
                    var parameters = new SqlParameter[] { new SqlParameter("@name", characterName) };

                    DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong SetChar_Offline: {ex.Message}");

                // Fallback sử dụng SQL trực tiếp nếu có lỗi
                var query = "UPDATE TBL_XWWL_Char SET FLD_ONLINE = 0 WHERE FLD_NAME = @name";
                var parameters = new SqlParameter[] { new SqlParameter("@name", characterName) };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
        }

        /// <summary>
        /// Tạo ID duy nhất cho vật phẩm
        /// </summary>
        /// <returns>ID duy nhất cho vật phẩm</returns>
        private static readonly object _lock = new();
        private static long _currentId = 0;
        private static long _maxId = 0;

        public static long CreateItemSeries()
        {
            lock (_lock)
            {
                try
                {
                    if (_currentId >= _maxId)
                    {
                        _currentId = long.Parse(DBA.GetDBValue_3("EXEC XWWL_GetItemSerial2 1000").ToString());
                        _maxId = _currentId + 1000;
                    }
                    return _currentId++;
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"CreateItemSeries Error: {ex.Message}");
                    return 0;
                }
            }
        }

        /// <summary>
        /// Lấy danh sách nhân vật online trong bang phái
        /// </summary>
        /// <param name="character">Tên bang phái</param>
        /// <returns>DataTable chứa thông tin nhân vật hoặc null nếu không tìm thấy</returns>
        public static int DatDuocNhanVatBangPhaiOnline(string character)
        {
            try
            {
                if (string.IsNullOrEmpty(character))
                {
                    return 0;
                }

                // Lấy danh sách nhân vật online trong bang phái
                var query = "SELECT  COUNT(*)  FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =  @name and FLD_ONLINE = 1";
                var parameters = new SqlParameter[] { new SqlParameter("@name", character) };

                var dataTable = DBA.GetDBToDataTable(query, parameters);
                return dataTable.Rows.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong DatDuocNhanVatBangPhaiOnline: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Ghi lại thông tin tổng hợp vật phẩm
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="userName">Tên người dùng</param>
        /// <param name="itemId">ID vật phẩm</param>
        /// <param name="itemName">Tên vật phẩm</param>
        /// <param name="itemCount">Số lượng vật phẩm</param>
        /// <param name="itemProperties">Thuộc tính vật phẩm</param>
        /// <param name="type">Loại hình</param>
        public static void SyntheticRecord(string string_0, string string_1, string string_2, int int_0, string string_3, string string_4, X_Vat_Pham_Loai VatPhamCLass_0)
        {
            try
            {
                if (World.ItemRecord != 1)
                {
                    return;
                }

                // Ghi lại thông tin tổng hợp vật phẩm
                var query = "INSERT  INTO  SyntheticRecord(FLD_ID,FLD_NAME,FLD_QJID,FLD_PID,FLD_INAME,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_TYPE,FLD_CZID,FLD_SUCCESS,FLD_QHJD)      VALUES      (@UserId,@UserName,@qjid,@pid,@iname,@magic0,@magic1,@magic2,@magic3,@magic4,@type,@czid,@success,@qhjd)";
                var parameters = new SqlParameter[14]
                {
                    SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
                    SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
                    SqlDBA.MakeInParam("@qjid", SqlDbType.Int, 4, (int)VatPhamCLass_0.GetItemGlobal_ID),
                    SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, (int)VatPhamCLass_0.GetVatPham_ID),
                    SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, VatPhamCLass_0.GetItemName()),
                    SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC0),
                    SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC1),
                    SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC2),
                    SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC3),
                    SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC4),
                    SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3),
                    SqlDBA.MakeInParam("@czid", SqlDbType.Int, 4, int_0),
                    SqlDBA.MakeInParam("@success", SqlDbType.VarChar, 30, string_4),
                    SqlDBA.MakeInParam("@qhjd", SqlDbType.Int, 4, VatPhamCLass_0.FLD_CuongHoaSoLuong)
                };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong SyntheticRecord: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật trạng thái ly hôn
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        public static void LyHonTrangThai(string characterName)
        {
            try
            {
                // Sử dụng SQL trực tiếp để cập nhật trạng thái ly hôn
                DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_QlNAME='{1}',FLD_QlDu={2},FLD_LOVE_WORD='{3}',FLD_MARITAL_STATUS={4},FLD_MARRIED={5}  WHERE  FLD_NAME='{0}'", characterName, string.Empty, 0, string.Empty, 0, 0)).GetAwaiter().GetResult();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong LyHonTrangThai: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa địa chỉ IP kiểm tra của tài khoản
        /// </summary>
        /// <param name="accountId">ID tài khoản</param>
        public static void Clear_FLD_CHECKIP(string accountId)
        {
            DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_ONLINE= 0, FLD_CHECKLOGIN = 'false', FLD_CHECKIP = null, FLD_LANIP = null  WHERE FLD_ID = '" + accountId + "'", "rxjhaccount").GetAwaiter().GetResult();
        }

        /// <summary>
        /// Đặt trạng thái người dùng thành online
        /// </summary>
        /// <param name="accountId">ID tài khoản</param>
        /// <param name="ip">Địa chỉ IP</param>
        public static void SetUserOnline(string accountId, string ip)
        {
            try
            {
                // Sử dụng SQL trực tiếp để cập nhật trạng thái người dùng
                DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_ONLINE= 1, FLD_LANIP = '" + ip + "' WHERE FLD_ID = '" + accountId + "'", "rxjhaccount").GetAwaiter().GetResult();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong SetUserOnline: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy tên bang phái của người dùng
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        /// <returns>Tên bang phái hoặc chuỗi rỗng nếu không tìm thấy</returns>
        public static DataTable GetUserNameBp(string characterName)
        {
            try
            {
                // Lấy tên bang phái của người dùng
                var dBToDataTable = DBA.GetDBToDataTable("EXEC  XWWL_LOAD_Guild  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, characterName) });
                if (dBToDataTable == null)
                {
                    return null;
                }
                if (dBToDataTable.Rows.Count == 0)
                {
                    return null;
                }
                return dBToDataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong GetUserNameBp: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Đặt trạng thái nhận quà lần đầu
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        public static void SetNhanQuaLanDau(string characterName)
        {
            try
            {
                // Đặt trạng thái nhận quà lần đầu
                var query = "UPDATE TBL_XWWL_Char SET FLD_NHANQUALANDAU = 1 WHERE FLD_NAME = @name";
                var parameters = new SqlParameter[] { new SqlParameter("@name", characterName) };

                DBA.ExeSqlCommand(query, parameters).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong SetNhanQuaLanDau: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra trạng thái nhận quà lần đầu
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        /// <returns>1 nếu đã nhận, 0 nếu chưa nhận, -1 nếu có lỗi</returns>
        public static int CheckNhanQuaLanDau(string characterName)
        {
            try
            {
                // Kiểm tra trạng thái nhận quà lần đầu
                var query = $"SELECT FLD_NHANQUALANDAU FROM TBL_XWWL_Char WHERE FLD_NAME = '{characterName}'";

                var result = DBA.GetDBValue_3(query);
                return result != null ? Convert.ToInt32(result) : -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong CheckNhanQuaLanDau: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Kiểm tra số lượng võ huấn mỗi ngày
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        /// <returns>Số lượng võ huấn đã tham gia hoặc -1 nếu có lỗi</returns>
        public static int CheckSoLuongVoHuanMoiNgay(string characterName, string daterecieve)
        {
            try
            {
                var dBToDataTable = DBA.GetDBToDataTable("SELECT  VOHUAN_GIOIHAN_THEONGAY  FROM  TBL_XWWL_Char  WHERE VOHUAN_TIME = @daterec and  FLD_NAME=@name", new SqlParameter[2]
                {
                    SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, characterName),
                    SqlDBA.MakeInParam("@daterec", SqlDbType.VarChar, 30, daterecieve)
                });
                if (dBToDataTable == null)
                {
                    return 0;
                }
                if (dBToDataTable.Rows.Count != 0)
                {
                    return int.Parse(dBToDataTable.Rows[0][0].ToString());
                }
                return 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong CheckSoLuongVoHuanMoiNgay: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Cập nhật số lượng võ huấn mỗi ngày
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        /// <param name="count">Số lượng võ huấn</param>
        public static void UpdateVoHuanMoiNgay(string characterName, string date, int count)
        {
            try
            {
                // Cập nhật số lượng võ huấn mỗi ngày
                DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  VOHUAN_GIOIHAN_THEONGAY={1}, VOHUAN_TIME='{2}'  WHERE  FLD_NAME='{0}'", characterName, count, date)).GetAwaiter().GetResult();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong UpdateVoHuanMoiNgay: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo mới tiểu sử nhân vật
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        /// <param name="biography">Tiểu sử</param>
        public static void CreateBiography(string string_0, string string_1, int int_0, string string_2, int int_1)
        {
            try
            {
                // Tạo mới tiểu sử nhân vật
                DBA.GetDBValue_3("EXEC  INT_CS_DATA_New  @fname,  @sname,  @msg,  @npcid,@type", new SqlParameter[5]
                {
                    SqlDBA.MakeInParam("@fname", SqlDbType.VarChar, 30, string_0),
                    SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, string_1),
                    SqlDBA.MakeInParam("@msg", SqlDbType.VarChar, 2000, string_2),
                    SqlDBA.MakeInParam("@npcid", SqlDbType.Int, 0, int_0),
                    SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, int_1)
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi trong CreateBiography: {ex.Message}");
            }
        }

        /// <summary>
        /// Đặt trạng thái đã xem truyền thư
        /// </summary>
        /// <param name="characterName">Tên nhân vật</param>
        public static void SetTruyenThuDaXem(int int_0, int int_1)
        {
            DBA.ExeSqlCommand("UPDATE  TBL_TruyenThuHeThong  SET  DanhDauDaXem=@rd  WHERE  ID=@id", new SqlParameter[2]
            {
            SqlDBA.MakeInParam("@rd", SqlDbType.Int, 0, int_1),
            SqlDBA.MakeInParam("@id", SqlDbType.Int, 30, int_0)
            }).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Tạo mới quan hệ sư đồ
        /// </summary>
        /// <param name="masterName">Tên sư phụ</param>
        /// <param name="discipleName">Tên đệ tử</param>
        /// <returns>Kết quả thực hiện (1: thành công, -1: thất bại)</returns>
        public static int TaoMoiQuanHeSuDo(string string_0, string string_1, int int_0, int int_1)
        {
            var dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  FLD_TNAME  FROM  TBL_SuDoSoLieu  WHERE  FLD_TNAME=@name", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable == null)
            {
                return -1;
            }
            if (dBToDataTable.Rows.Count == 0)
            {
                dBToDataTable.Dispose();
                if (DBA.ExeSqlCommand(string.Format("EXEC  INT_St_DATA  @sname,@tname,@tlevel,@index", string_1, string_0, int_0, int_1), new SqlParameter[4]
                {
                SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, string_1),
                SqlDBA.MakeInParam("@tname", SqlDbType.VarChar, 30, string_0),
                SqlDBA.MakeInParam("@tlevel", SqlDbType.Int, 0, int_0),
                SqlDBA.MakeInParam("@index", SqlDbType.Int, 0, int_1)
                }).GetAwaiter().GetResult() != -1)
                {
                    return 1;
                }
                return -1;
            }
            dBToDataTable.Dispose();
            return -1;
        }

        /// <summary>
        /// Giải trừ quan hệ thầy trò
        /// </summary>
        /// <param name="masterName">Tên sư phụ</param>
        /// <param name="discipleName">Tên đệ tử</param>
        /// <returns>Kết quả thực hiện (1: thành công, -1: thất bại)</returns>
        public static int GiaiTruQuanHeThayTro(string string_0, string string_1)
        {
            if (DBA.ExeSqlCommand($"delete  [TBL_SuDoSoLieu]  WHERE  FLD_TNAME  ='{string_0}'  and  FLD_SNAME='{string_1}'", "GameServer").GetAwaiter().GetResult() != -1)
            {
                return 1;
            }
            return -1;
        }

        public static void BackupDatabase(string Database, string Disk)
        {
            try
            {
                var string_ = "BACKUP DATABASE @Database TO DISK= @Disk";
                var sqlParameter_ = new SqlParameter[2]
                {
                SqlDBA.MakeInParam("@Database", SqlDbType.VarChar, 30, Database),
                SqlDBA.MakeInParam("@Disk", SqlDbType.VarChar, 100, Disk)
                };
                DBA.ExeSqlCommand(string_, sqlParameter_).GetAwaiter().GetResult();
            }
            catch
            {
            }
        }
        public static int SetUserName(string string_0, string string_1, int int_0, byte[] byte_0)
        {
            var array = new byte[76];
            var array2 = new byte[1095];
            var array3 = new byte[2736];
            var array4 = new byte[381];
            var bytes = BitConverter.GetBytes(CreateItemSeries());
            var src = new byte[4];
            var bytes2 = BitConverter.GetBytes(1);
            switch (int_0)
            {
                case 1:
                    src = BitConverter.GetBytes(100200002);
                    break;
                case 2:
                    src = BitConverter.GetBytes(200200002);
                    break;
                case 3:
                    src = BitConverter.GetBytes(300200002);
                    break;
                case 4:
                    src = BitConverter.GetBytes(400200002);
                    break;
                case 5:
                    src = BitConverter.GetBytes(500200002);
                    break;
                case 6:
                    src = BitConverter.GetBytes(700200002);
                    break;
                case 7:
                    src = BitConverter.GetBytes(800200001);
                    break;
                case 8:
                    src = BitConverter.GetBytes(100204001);
                    break;
                case 9:
                    src = BitConverter.GetBytes(200204001);
                    break;
                case 10:
                    src = BitConverter.GetBytes(900200001);
                    break;
                case 11:
                    src = BitConverter.GetBytes(400204001);
                    break;
                case 12:
                    src = BitConverter.GetBytes(300204001);
                    break;
                case 13:
                    src = BitConverter.GetBytes(500204001);
                    break;
            }
            System.Buffer.BlockCopy(bytes, 0, array, 0, 4);
            System.Buffer.BlockCopy(src, 0, array, 8, 4);
            System.Buffer.BlockCopy(bytes2, 0, array, 12, 4);
            System.Buffer.BlockCopy(array, 0, array3, 0, 76);
            var num = 0;
            var dBToDataTable = DBA.GetDBToDataTable("Select FLD_INDEX FROM TBL_XWWL_Char Where FLD_ID=@FLD_ID", new SqlParameter[1] { SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable.Rows.Count >= 4)
            {
                dBToDataTable.Dispose();
                return -1;
            }
            if (dBToDataTable.Rows.Count == 0)
            {
                num = 0;
            }
            else
            {
                List<int> list = new();
                for (var i = 0; i < dBToDataTable.Rows.Count; i++)
                {
                    var item = (int)dBToDataTable.Rows[i]["FLD_INDEX"];
                    list.Add(item);
                }
                for (var j = 0; j < 4; j++)
                {
                    if (!list.Contains(j))
                    {
                        num = j;
                        break;
                    }
                }
            }
            dBToDataTable.Dispose();
            var num2 = 0;
            var num3 = 0;
            switch (int_0)
            {
                case 4:
                    num2 = 124;
                    num3 = 116;
                    break;
                case 6:
                    num2 = 130;
                    num3 = 114;
                    break;
                case 7:
                    num2 = 124;
                    num3 = 136;
                    break;
                case 1:
                case 8:
                    num2 = 145;
                    num3 = 116;
                    break;
                case 10:
                    num2 = 145;
                    num3 = 116;
                    break;
                case 11:
                    num2 = 124;
                    num3 = 116;
                    break;
                case 2:
                case 3:
                case 5:
                case 9:
                case 12:
                    num2 = 133;
                    num3 = 118;
                    break;
                case 13:
                    num2 = 118;
                    num3 = 136;
                    break;
            }
            if (DBA.ExeSqlCommand("EXEC XWWL_INT_USER_DATA @FLD_ID,@name,@rwid,@zy,@hp,@mp,@coue,@xrwhex,@xrwhex2", new SqlParameter[9]
            {
            SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, string_0),
            SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_1),
            SqlDBA.MakeInParam("@rwid", SqlDbType.Int, 0, num),
            SqlDBA.MakeInParam("@zy", SqlDbType.Int, 0, int_0),
            SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, num2),
            SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, num3),
            SqlDBA.MakeInParam("@coue", SqlDbType.VarBinary, 10, byte_0),
            SqlDBA.MakeInParam("@xrwhex", SqlDbType.VarBinary, array2.Length, array2),
            SqlDBA.MakeInParam("@xrwhex2", SqlDbType.VarBinary, array3.Length, array3)
            }).GetAwaiter().GetResult() == -1)
            {
                return -1;
            }
            return 1;
        }
        public static void SetChar_Online(string id, string username)
        {
            var string_ = "UPDATE TBL_XWWL_CHAR SET FLD_ONLINE= 1 WHERE FLD_ID = '" + id + "'AND FLD_NAME= '" + username + "'";
            DBA.ExeSqlCommand(string_, "GameServer").GetAwaiter().GetResult();
        }

        public static DataTable GetUserPublicWarehouse(string string_0)
        {
            var dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count != 0)
            {
                return dBToDataTable;
            }
            Converter.ToString1(new byte[World.Item_Db_Byte_Length * 60]);
            Converter.ToString1(new byte[60]);
            DBA.ExeSqlCommand("EXEC  XWWL_CREATE_ID_BANK      @Userid,@aaa,@ck,@ck1", new SqlParameter[4]
            {
            SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
            SqlDBA.MakeInParam("@aaa", SqlDbType.Int, 0, 0),
            SqlDBA.MakeInParam("@ck", SqlDbType.VarBinary, World.Item_Db_Byte_Length * 60, new byte[World.Item_Db_Byte_Length * 60]),
            SqlDBA.MakeInParam("@ck1", SqlDbType.VarBinary, 50, new byte[50])
            }).GetAwaiter().GetResult();
            var dBToDataTable2 = DBA.GetDBToDataTable($"select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID='{string_0}'", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable2 == null)
            {
                return null;
            }
            if (dBToDataTable2.Rows.Count == 0)
            {
                return null;
            }
            return dBToDataTable2;
        }
        public static DataTable GetUserWarehouse(string string_0, string string_1)
        {
            var dBToDataTable = DBA.GetDBToDataTable("select  *  from    [TBL_XWWL_Warehouse]    where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
            {
            SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
            SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
            });
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count != 0)
            {
                return dBToDataTable;
            }
            DBA.ExeSqlCommand("EXEC  XWWL_CREATE_USER_BANK  @Userid,@Username,@aa,@zb", new SqlParameter[4]
            {
            SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
            SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1),
            SqlDBA.MakeInParam("@aa", SqlDbType.Int, 0, 0),
            SqlDBA.MakeInParam("@zb", SqlDbType.VarBinary, World.Item_Db_Byte_Length * 60, new byte[World.Item_Db_Byte_Length * 60])
            }).GetAwaiter().GetResult();
            var dBToDataTable2 = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Warehouse]  where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
            {
            SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
            SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
            });
            if (dBToDataTable2 == null)
            {
                return null;
            }
            if (dBToDataTable2.Rows.Count == 0)
            {
                return null;
            }
            return dBToDataTable2;
        }
        public static void StoreRecord(string string_0, string string_1, int int_0, string string_2, string string_3, int int_1, long long_0, int int_2, int int_3, int int_4, int int_5, int int_6)
        {
            if (World.StoreRecord == 1)
            {
                var sql = "INSERT  INTO  StoreRecord  (FLD_ID,FLD_NAME,FLD_PID,FLD_INAME,FLD_TYPE,FLD_NUM,FLD_PRICE,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4)      VALUES      (@UserId,@UserName,@pid,@iname,@type,@number,@price,@magic0,@magic1,@magic2,@magic3,@magic4)";
                var prams = new SqlParameter[12]
                {
                SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
                SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
                SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
                SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
                SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3),
                SqlDBA.MakeInParam("@number", SqlDbType.Int, 4, int_1),
                SqlDBA.MakeInParam("@price", SqlDbType.VarChar, 50, long_0.ToString()),
                SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, int_2),
                SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, int_3),
                SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, int_4),
                SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, int_5),
                SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, int_6)
                };
                DBA.ExeSqlCommand(sql, prams).GetAwaiter().GetResult();
                // World.SqlPool.Enqueue(new DbPoolClass
                // {
                // 	Conn = DBA.getstrConnection(null),
                // 	Prams = prams,
                // 	Sql = sql,
                // 	Type = 1
                // });
            }
        }

        public static void BachBaoCacRecord(string string_0, string string_1, double double_0, string string_2, int int_0, int int_1)
        {
            DBA.ExeSqlCommand($"INSERT  INTO  BachBaoCacRecord (UserId,UserName,VatPham_ID,VatPhamTen,VatPhamSoLuong,NguyenBaoSoLuong)  VALUES  ('{string_0}','{string_1}','{double_0}','{string_2}',{int_0},{int_1})").GetAwaiter().GetResult();
        }
        public static void DropRecord(string string_0, string string_1, long long_0, int int_0, string string_2, int int_1, int int_2, int int_3, int int_4, int int_5, int int_6, int int_7, int int_8, string string_3)
        {
            if (World.DropRecord == 1)
            {
                var sql = "INSERT  INTO  DropRecord  (FLD_ID,FLD_NAME,FLD_QJID,FLD_PID,FLD_INAME,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_MAP,FLD_X,FLD_Y,FLD_TYPE)      VALUES      (@UserId,@UserName,@qjid,@pid,@iname,@magic0,@magic1,@magic2,@magic3,@magic4,@map,@x,@y,@type)";
                var prams = new SqlParameter[14]
                {
                SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
                SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
                SqlDBA.MakeInParam("@qjid", SqlDbType.Int, 4, long_0),
                SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
                SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
                SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, int_1),
                SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, int_2),
                SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, int_3),
                SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, int_4),
                SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, int_5),
                SqlDBA.MakeInParam("@map", SqlDbType.Int, 4, int_6),
                SqlDBA.MakeInParam("@x", SqlDbType.Int, 4, int_7),
                SqlDBA.MakeInParam("@y", SqlDbType.Int, 4, int_8),
                SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3)
                };
                DBA.ExeSqlCommand(sql, prams).GetAwaiter().GetResult();
                // World.SqlPool.Enqueue(new DbPoolClass
                // {
                // 	Conn = DBA.getstrConnection(null),
                // 	Prams = prams,
                // 	Sql = sql,
                // 	Type = 1
                // });
            }
        }

        public static void AddGuildPoint(int point, string name)
        {
            DBA.ExeSqlCommand("UPDATE TBL_XWWL_GuildMember SET FLD_GuildPoint=FLD_GuildPoint+@point,  FLD_NewGuildPoint=FLD_NewGuildPoint+@point  WHERE FLD_NAME=@Username", new SqlParameter[2]
            {
            SqlDBA.MakeInParam("@point", SqlDbType.Int, 0, point),
            SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, name)
            }).GetAwaiter().GetResult();
        }
        public static void Update_Gold_Extra(string string_0, int int_0)
        {
            DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_MONEYEXTRALEVEL={1}  WHERE  FLD_NAME='{0}'", string_0, int_0)).GetAwaiter().GetResult();
        }
        public static void DrugRecord(string string_0, string string_1, int int_0, string string_2, int int_1)
        {
            if (World.DrugRecord == 1)
            {
                var sql = "INSERT  INTO  DrugRecord(FLD_ID,FLD_NAME,FLD_PID)  VALUES  (@UserId,@UserName,@pid)";
                var prams = new SqlParameter[5]
                {
                SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
                SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
                SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
                SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
                SqlDBA.MakeInParam("@number", SqlDbType.Int, 4, int_1)
                };
                DBA.ExeSqlCommand(sql, prams).GetAwaiter().GetResult();
                // World.SqlPool.Enqueue(new DbPoolClass
                // {
                // 	Conn = DBA.getstrConnection(null),
                // 	Prams = prams,
                // 	Sql = sql,
                // 	Type = 1
                // });
            }
        }
        public static int Update_MonPhai_LienMinh_MinhChu(string Ten_BangPhai, string Ten_BangChu, int ThongBaoCongThanh_Status)
        {
            var string_ = $"UPDATE TBL_XWWL_Guild SET LienMinh_MinhChu='{Ten_BangChu}',ThongBao_CongThanh={ThongBaoCongThanh_Status} WHERE G_Name='{Ten_BangPhai}'";
            return DBA.ExeSqlCommand(string_).GetAwaiter().GetResult();
        }

        public static int DoiMoi_LienMinh_ThongBao_CongThanh_TrangThai(string Ten_MinhChu, int ThongBaoCongThanh_Status)
        {
            var string_ = $"UPDATE TBL_XWWL_Guild SET ThongBao_CongThanh={ThongBaoCongThanh_Status} WHERE LienMinh_MinhChu='{Ten_MinhChu}'";
            return DBA.ExeSqlCommand(string_).GetAwaiter().GetResult();
        }
        public static int CapNhat_ThienMaThanCung_TinTuc(string ChiemLinhMonPhai, string ChiemLinhNgay, int CuaThanh_CuongHoa_Level)
        {
            return DBA.ExeSqlCommand($"UPDATE ThienMaThanCung_DanhSach SET Bang_Chiem_Thanh='{ChiemLinhMonPhai}',Ngay_Chiem_Thanh={ChiemLinhNgay},Cong_Thanh_CuongHoa_Level={CuaThanh_CuongHoa_Level},ThoiGian_LamMoi_CongThanh='{DateTime.Now.ToString()}'").GetAwaiter().GetResult();
        }
        public static DataTable DatDuoc_ThienMa_ThanCu_ChiemLinh_TinTuc()
        {
            var string_ = "SELECT * FROM ThienMaThanCung_DanhSach";
            var dBToDataTable = DBA.GetDBToDataTable(string_);
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count == 0)
            {
                return null;
            }
            return dBToDataTable;
        }
        public static int InsertCashShopLog(string status, string message, string buyer, int amount, double price,
            int marketId, string productId)
        {
            var query = @"INSERT INTO CASH_SHOP_LOG (STATUS, MESSAGE, USERNAME, AMOUNT, PRICE, ITEM_ID, PRODUCT_ID) 
              VALUES (@Status, @Message, @Username, @Amount, @Price, @ItemId, @ProductId)";

            var parameters = new[]
            {
            new SqlParameter("@Status", status),
            new SqlParameter("@Message", message),
            new SqlParameter("@Username", buyer),
            new SqlParameter("@Amount", amount),
            new SqlParameter("@Price", price),
            new SqlParameter("@ItemId", marketId),
            new SqlParameter("@ProductId", productId)
        };

            var insertedId = DBA.ExeSqlCommand(query, parameters, "BBG").GetAwaiter().GetResult();
            return insertedId;
        }
        public static int UpdateBachBaoNewRecord(int id, string status)
        {
            LogHelper.WriteLine(LogLevel.Error, $"Update Cash {id} - {status}");
            var query = $"UPDATE CASH_SHOP_LOG SET STATUS = '{status}' WHERE ID = {id}";
            return DBA.ExeSqlCommand(query, "BBG").GetAwaiter().GetResult();
        }
        public static DataTable DatDuocMasterData(string string_0)
        {
            var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_SuDoSoLieu  WHERE  FLD_TNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count != 0)
            {
                return dBToDataTable;
            }
            return null;
        }

        public static DataTable DatDuocApprenticeData(string string_0)
        {
            var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_SuDoSoLieu  WHERE  FLD_SNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count != 0)
            {
                return dBToDataTable;
            }
            return null;
        }

        public static void TangThemVoHuan_Record(string UserID, string UserName, int WuXun, string 说明)
        {
            if (WuXun != 0)
            {
                DBA.ExeSqlCommand($"INSERT  INTO  武勋记录  (UserId,UserName,WuXun,ShuoMing)  VALUES  ('{UserID}','{UserName}',{WuXun},'{说明}')", "GameLog").GetAwaiter().GetResult();
            }
        }
        public static DataTable DatDuocDanhSach_TruyenThu(string string_0)
        {
            var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_TruyenThuHeThong  WHERE  NguoiNhanThu_NhatVatTen  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count != 0)
            {
                return dBToDataTable;
            }
            dBToDataTable.Dispose();
            return null;
        }
        public static string DatDuoc_MonPhai_LienMinh_MinhChu(string 门派名)
        {
            var string_ = "select * from TBL_XWWL_Guild where G_Name=@name";
            var sqlParameter_ = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 门派名) };
            var dBToDataTable = DBA.GetDBToDataTable(string_, sqlParameter_);
            if (dBToDataTable == null)
            {
                return "";
            }
            if (dBToDataTable.Rows.Count == 0)
            {
                dBToDataTable.Dispose();
                return "";
            }
            var result = dBToDataTable.Rows[0]["LienMinh_MinhChu"].ToString();
            dBToDataTable.Dispose();
            return result;
        }

        public static DataTable Load_ThongTinLienMinh()
        {
            var string_ = "select * from TBL_XWWL_Guild where LienMinh_MinhChu !='' and LienMinh_MinhChu=G_Name";
            var dBToDataTable = DBA.GetDBToDataTable(string_);
            if (dBToDataTable == null)
            {
                return null;
            }
            if (dBToDataTable.Rows.Count == 0)
            {
                dBToDataTable.Dispose();
                return null;
            }
            return dBToDataTable;
        }
        public static void SetUserHoldRefresh()
        {
            var accountDbContext = HeroYulgang.Core.DatabaseManager.Instance.AccountDb;
            accountDbContext.TblAccounts.Where(x => x.FldChecklogin == true).ToList().ForEach(x =>
            {
                x.FldChecklogin = false;
                x.FldCheckip = null;
            });
            accountDbContext.SaveChanges();
            // DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_CHECKLOGIN='false',  FLD_CHECKIP = null WHERE FLD_CHECKLOGIN = 'true'", "rxjhaccount");
        }
    }
}
