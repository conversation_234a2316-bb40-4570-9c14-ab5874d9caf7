Lỗi khi thực hiện truy vấn ExecuteDbCommand: Invalid column name 'FLD_GUILD_NAME'. SELECT * FROM TBL_XWWL_Char WHERE FLD_ONLINE = 1 AND FLD_GUILD_NAME = @guildName
Lỗi khi thực hiện truy vấn ExecuteDbCommand: Invalid column name 'FLD_GUILD_NAME'. SELECT * FROM TBL_XWWL_Char WHERE FLD_ONLINE = 1 AND FLD_GUILD_NAME = @guildName
Xử lý message: Đã xử lý tin nhắn thành công
:Length<22
:Length<22
Lỗi khi thực hiện truy vấn ExecuteDbCommand: Procedure or function 'XWWL_UPDATE_USER_DATA_NEW' expects parameter '@id', which was not supplied. XWWL_UPDATE_USER_DATA_NEW
Lỗi khi thực hiện truy vấn ExecuteDbCommand: Procedure or function 'XWWL_UPDATE_USER_Warehouse' expects parameter '@id', which was not supplied. XWWL_UPDATE_USER_Warehouse
Lỗi khi thực hiện truy vấn ExecuteDbCommand: Procedure or function 'XWWL_UPDATE_ID_Warehouse' expects parameter '@id', which was not supplied. XWWL_UPDATE_ID_Warehouse
Lỗi khi thực hiện truy vấn ExecuteDbCommand: Invalid column name 'FLD_GUILD_NAME'. SELECT * FROM TBL_XWWL_Char WHERE FLD_ONLINE = 1 AND FLD_GUILD_NAME = @guildName
Lỗi khi thực hiện truy vấn ExecuteDbCommand: Invalid column name 'FLD_GUILD_NAME'. SELECT * FROM TBL_XWWL_Char WHERE FLD_ONLINE = 1 AND FLD_GUILD_NAME = @guildName

 same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
Lỗi trong DatDuocNhanVatBangPhaiOnline: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
Lỗi trong DatDuocNhanVatBangPhaiOnline: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
Lỗi trong DatDuocNhanVatBangPhaiOnline: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
Lỗi trong DatDuocNhanVatBangPhaiOnline: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
Lỗi trong DatDuocNhanVatBangPhaiOnline: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.

[2025-05-28 20:52:14] [Cảnh báo] Cập nhật trạng thái GameServer thất bại: Lỗi hệ thống: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
[2025-05-28 20:52:32] [Debug] Đã nhận packet trước khi đăng nhập: 143
[2025-05-28 20:52:33] [Debug] Đã nhận packet trước khi đăng nhập: 5
[2025-05-28 20:52:33] [Debug] Vào game [1] - [ChanCung]
[2025-05-28 20:52:34] [Debug] Đã gửi yêu cầu xác định ClientActor cho người chơi ChanCung
[2025-05-28 20:52:34] [Thông tin] Người chơi ChanCung đã vào zone 0
[2025-05-28 20:52:34] [Thông tin] Sending to LS: Thu hoach Server liet bieu |1|0|192.168.1.9|0|0
[2025-05-28 20:52:34] [Thông tin] Nhận từ LS: Đã xử lý tin nhắn thành công, Success: True
[2025-05-28 20:52:34] [Debug] Xử lý message: Đã xử lý tin nhắn thành công
[2025-05-28 20:52:41] [Debug] [1][ChanCung][][170][11](1C-2T)[1] - (Kênh: 1) : asd
[2025-05-28 20:52:42] [Thông tin] Client kết nối: 192.168.1.9:52228 tới máy chủ 192.168.1.9:13000
[2025-05-28 20:52:42] [Debug] Allocated Player SessionID: 1001
[2025-05-28 20:52:42] [Thông tin] Client kết nối: 192.168.1.9:52228 (SessionID: 1001) tới máy chủ 192.168.1.9:13000
[2025-05-28 20:52:42] [Debug] Đã tạo ActorNetState mới cho phiên 1001 từ 192.168.1.9:52228
[2025-05-28 20:52:43] [Debug] Yêu cầu đăng nhập từ 2
[2025-05-28 20:52:43] [Debug] KetNoi_DangNhap Account 2
[2025-05-28 20:52:43] [Debug] Player ID 2 (SessionID: 1001) đã kết nối 1001 
[2025-05-28 20:52:43] [Debug] Đã chuyển đổi Client của PlayersBes sang ActorNetState
[2025-05-28 20:52:43] [Thông tin] Đã chuyển đổi thành công người chơi  sang ActorNetState
[2025-05-28 20:52:43] [Debug] Đã cập nhật Client của người chơi  sang ActorNetState
[2025-05-28 20:52:43] [Debug] Đã thiết lập player context cho PacketHandlerActor cho người chơi 
[2025-05-28 20:52:43] [Lỗi]  KetNoi_DangNhap Error[1001]-[192.168.1.9]Object reference not set to an instance of an object.
[2025-05-28 20:52:43] [Debug] Người dùng 2 đã đăng nhập thành công
[2025-05-28 20:52:43] [Debug] Đã thiết lập player context cho 2 (SessionID: 1001)
[2025-05-28 20:52:47] [Debug] Đã nhận packet trước khi đăng nhập: 176
[2025-05-28 20:52:57] [Debug] Đã nhận packet trước khi đăng nhập: 176
[2025-05-28 20:53:02] [Debug] Đã nhận packet trước khi đăng nhập: 8
[2025-05-28 20:53:04] [Debug] Đã nhận packet trước khi đăng nhập: 8
[2025-05-28 20:53:05] [Debug] Đã nhận packet trước khi đăng nhập: 8
[2025-05-28 20:53:07] [Debug] Đã nhận packet trước khi đăng nhập: 176
[2025-05-28 20:53:17] [Debug] Đã nhận packet trước khi đăng nhập: 176
[2025-05-28 20:53:22] [Thông tin] Đã mở thư mục log: /home/<USER>/Documents/HeroLinuxNet9/HeroYulgang/bin/Debug/net9.0/logs
[2025-05-28 20:53:26] [Thông tin] Client ngắt kết nối: 192.168.1.9:52228 (SessionID: 1001)
[2025-05-28 20:53:26] [Debug] Released Player SessionID: 1001
[2025-05-28 20:53:27] [Debug] Đã nhận packet trước khi đăng nhập: 176
[2025-05-28 20:53:30] [Thông tin] Đang dừng máy chủ...
[2025-05-28 20:53:30] [Thông tin] Máy chủ đang dừng...
[2025-05-28 20:53:30] [Thông tin] Đang dừng World...
[2025-05-28 20:53:30] [Thông tin] Máy chủ mạng đã dừng
[2025-05-28 20:53:35] [Thông tin] Đã dừng task nhận tin nhắn từ LoginServer
[2025-05-28 20:53:35] [Thông tin] Đã ngắt kết nối từ LoginServer
[2025-05-28 20:53:35] [Thông tin] Đã dừng task nhận tin nhắn và ngắt kết nối LoginServer
[2025-05-28 20:53:35] [Thông tin] Máy chủ hiện đang offline
[2025-05-28 20:53:35] [Thông tin] World đã dừng thành công
[2025-05-28 20:53:35] [Thông tin] Máy chủ đã dừng thành công
[2025-05-28 20:53:35] [Debug] Đã đóng tất cả kết nối