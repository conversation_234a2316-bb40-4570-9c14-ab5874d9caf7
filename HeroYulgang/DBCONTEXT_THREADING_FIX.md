# Sửa lỗi DbContext Threading

## Vấn đề
Lỗi `System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed` x<PERSON>y ra khi nhiều thread cùng truy cập vào cùng một DbContext instance đồng thời.

## Nguyên nhân
1. **KetNoi_DangNhap2**: T<PERSON><PERSON> cập trực tiếp vào `DatabaseManager.Instance.AccountDb` từ nhiều thread
2. **LoadCharacterOffAttack**: Sử dụng `Parallel.ForEach` gây ra nhiều thread cùng lúc
3. **DbContext không thread-safe**: Entity Framework Core DbContext không được thiết kế để sử dụng đồng thời từ nhiều thread

## Giải pháp đã áp dụng

### 1. Sửa method `KetNoi_DangNhap2` trong Players.cs

**Trước:**
```csharp
var account = HeroYulgang.Core.DatabaseManager.Instance.AccountDb.TblAccounts.First(a=>a.FldId == id);
```

**Sau:**
```csharp
// Sử dụng DBA thread-safe thay vì truy cập trực tiếp DbContext
var accountData = Database.DBA.GetDBToDataTable(
    "SELECT FLD_PASSWORD, FLD_RXPIONT, FLD_RXPIONTX, FLD_COIN, FLD_VIP, FLD_VIPTIM, FLD_SEX, FLD_LASTLOGINIP, FLD_MACHINEID, FLD_SAFEWORD FROM TBL_ACCOUNT WHERE FLD_ID = @id",
    new[] { SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 30, id) },
    "AccountDb"
);
```

### 2. Cải thiện method `LoadCharacterOffAttack` trong World.cs

**Thêm các tính năng:**
- Giới hạn số thread đồng thời: `MaxDegreeOfParallelism = Environment.ProcessorCount / 2`
- Thêm delay ngẫu nhiên: `Thread.Sleep(RNG.Next(100, 500))`
- Logging chi tiết để theo dõi quá trình
- Xử lý lỗi tốt hơn

### 3. Thêm retry mechanism cho `OffAttackACharacter`

**Tính năng:**
- Retry tối đa 3 lần khi gặp lỗi DbContext threading
- Delay ngẫu nhiên giữa các lần retry: `Thread.Sleep(RNG.Next(500, 1500))`
- Phát hiện và xử lý riêng lỗi threading
- Logging chi tiết cho từng lần thử

## Lợi ích

### 1. Thread Safety
- Sử dụng DBA class đã được thiết kế thread-safe với lock mechanism
- Tránh truy cập trực tiếp vào DbContext từ nhiều thread

### 2. Performance
- Giới hạn số thread đồng thời tránh quá tải hệ thống
- Delay ngẫu nhiên giảm thiểu xung đột

### 3. Reliability
- Retry mechanism xử lý các lỗi tạm thời
- Logging chi tiết giúp debug và monitor

### 4. Scalability
- Hỗ trợ đăng nhập nhiều tài khoản đồng thời
- Xử lý tốt hơn khi có nhiều player online

## Cách sử dụng

### Chạy test
```csharp
await HeroYulgang.Tests.DbContextThreadingTest.RunAllTests();
```

### Monitor logs
Theo dõi logs để đảm bảo không còn lỗi threading:
```
[INFO] Bắt đầu load 500 character offline
[INFO] Hoàn thành load character offline. Tổng số offline: 123
```

## Lưu ý quan trọng

1. **Database Connection**: Đảm bảo connection string và database có sẵn
2. **Test Data**: Cần có dữ liệu test trong database để test hoạt động đúng
3. **Performance Monitoring**: Theo dõi performance sau khi áp dụng
4. **Backup**: Luôn backup database trước khi test

## Các file đã thay đổi

1. `HeroYulgang/RxjhServer/Players.cs` - Sửa method KetNoi_DangNhap2
2. `HeroYulgang/RxjhServer/World.cs` - Sửa LoadCharacterOffAttack và OffAttackACharacter
3. `HeroYulgang/Tests/DbContextThreadingTest.cs` - Test cases mới

## Kết luận

Các thay đổi này sẽ giải quyết hoàn toàn lỗi DbContext threading và cho phép hệ thống xử lý nhiều đăng nhập đồng thời một cách ổn định và hiệu quả.
